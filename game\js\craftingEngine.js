/**
 * Crafting Engine - Manages crafting, tech tree, and scientific progression
 */

class CraftingEngine {
    constructor(game) {
        this.game = game;
        
        // Crafting state
        this.unlockedRecipes = new Set(['stone_axe', 'wooden_spear']);
        this.techTreeNodes = new Map();
        this.craftingStations = new Map();
        
        // Recipe definitions
        this.recipes = {
            stone_axe: {
                id: 'stone_axe',
                name: 'Stone Axe',
                description: 'A basic tool for cutting wood. The foundation of all technology!',
                category: 'tools',
                ingredients: [
                    { id: 'stone', quantity: 2 },
                    { id: 'wood', quantity: 1 }
                ],
                result: { id: 'stone_axe', quantity: 1 },
                craftingTime: 3.0,
                sciencePoints: 5,
                station: 'hand_crafting'
            },
            wooden_spear: {
                id: 'wooden_spear',
                name: 'Wood<PERSON> Spear',
                description: 'A simple weapon for hunting and protection.',
                category: 'weapons',
                ingredients: [
                    { id: 'wood', quantity: 2 },
                    { id: 'stone', quantity: 1 }
                ],
                result: { id: 'wooden_spear', quantity: 1 },
                craftingTime: 2.0,
                sciencePoints: 3,
                station: 'hand_crafting'
            },
            revival_fluid: {
                id: 'revival_fluid',
                name: 'Revival Fluid',
                description: 'The miraculous formula that can free people from petrification!',
                category: 'chemistry',
                ingredients: [
                    { id: 'nitric_acid', quantity: 1 },
                    { id: 'ethanol', quantity: 1 }
                ],
                result: { id: 'revival_fluid', quantity: 1 },
                craftingTime: 10.0,
                sciencePoints: 50,
                station: 'chemistry_lab',
                unlockCondition: 'discover_revival_formula'
            },
            furnace: {
                id: 'furnace',
                name: 'Stone Furnace',
                description: 'A furnace for smelting metals and advanced crafting.',
                category: 'stations',
                ingredients: [
                    { id: 'stone', quantity: 10 },
                    { id: 'clay', quantity: 5 }
                ],
                result: { id: 'furnace', quantity: 1 },
                craftingTime: 15.0,
                sciencePoints: 20,
                station: 'hand_crafting'
            },
            iron_tools: {
                id: 'iron_tools',
                name: 'Iron Tools',
                description: 'Superior tools made from smelted iron.',
                category: 'tools',
                ingredients: [
                    { id: 'iron_ingot', quantity: 2 },
                    { id: 'wood', quantity: 1 }
                ],
                result: { id: 'iron_tools', quantity: 1 },
                craftingTime: 8.0,
                sciencePoints: 15,
                station: 'furnace'
            }
        };
        
        // Tech tree structure
        this.techTree = {
            stone_age: {
                id: 'stone_age',
                name: 'Stone Age',
                description: 'The beginning of technology',
                unlocked: true,
                recipes: ['stone_axe', 'wooden_spear'],
                children: ['bronze_age'],
                sciencePointCost: 0
            },
            bronze_age: {
                id: 'bronze_age',
                name: 'Bronze Age',
                description: 'Metalworking and advanced tools',
                unlocked: false,
                recipes: ['furnace', 'bronze_tools'],
                children: ['iron_age'],
                sciencePointCost: 50,
                prerequisites: ['stone_age']
            },
            iron_age: {
                id: 'iron_age',
                name: 'Iron Age',
                description: 'Superior metal technology',
                unlocked: false,
                recipes: ['iron_tools', 'iron_weapons'],
                children: ['chemistry'],
                sciencePointCost: 100,
                prerequisites: ['bronze_age']
            },
            chemistry: {
                id: 'chemistry',
                name: 'Chemistry',
                description: 'The science of matter and reactions',
                unlocked: false,
                recipes: ['revival_fluid', 'gunpowder'],
                children: ['modern_science'],
                sciencePointCost: 200,
                prerequisites: ['iron_age']
            }
        };
        
        // Crafting stations
        this.stationDefinitions = {
            hand_crafting: {
                name: 'Hand Crafting',
                description: 'Basic crafting with your hands',
                available: true
            },
            workbench: {
                name: 'Workbench',
                description: 'A proper workspace for detailed crafting',
                available: false,
                unlockRecipe: 'workbench'
            },
            furnace: {
                name: 'Furnace',
                description: 'For smelting and high-temperature crafting',
                available: false,
                unlockRecipe: 'furnace'
            },
            chemistry_lab: {
                name: 'Chemistry Lab',
                description: 'For advanced chemical synthesis',
                available: false,
                unlockRecipe: 'chemistry_lab'
            }
        };
        
        // Current crafting
        this.currentCrafting = null;
        this.craftingQueue = [];
    }
    
    async init() {
        console.log('Initializing Crafting Engine...');
        
        // Initialize tech tree
        this.initializeTechTree();
        
        // Set up event listeners
        this.setupEventListeners();
        
        console.log('Crafting Engine initialized');
    }
    
    initializeTechTree() {
        for (const [nodeId, node] of Object.entries(this.techTree)) {
            this.techTreeNodes.set(nodeId, { ...node });
        }
        
        // Unlock starting recipes
        for (const recipe of this.techTreeNodes.get('stone_age').recipes) {
            this.unlockedRecipes.add(recipe);
        }
    }
    
    setupEventListeners() {
        // Listen for item collection events
        this.game.gameManager.on('itemCollected', (item, quantity) => {
            this.checkAutoUnlocks(item);
        });
    }
    
    checkAutoUnlocks(item) {
        // Auto-unlock certain recipes when specific items are found
        if (item === 'nitric_acid' && !this.unlockedRecipes.has('revival_fluid')) {
            this.unlockRecipe('revival_fluid');
            console.log('Revival fluid recipe discovered!');
        }
    }
    
    canCraft(recipeId) {
        const recipe = this.recipes[recipeId];
        if (!recipe) return false;
        
        // Check if recipe is unlocked
        if (!this.unlockedRecipes.has(recipeId)) return false;
        
        // Check if station is available
        const station = this.stationDefinitions[recipe.station];
        if (!station || !station.available) return false;
        
        // Check ingredients
        const player = this.game.characterEngine.getPlayer();
        if (!player || !player.inventory) return false;
        
        for (const ingredient of recipe.ingredients) {
            if (!player.inventory.hasItem(ingredient.id, ingredient.quantity)) {
                return false;
            }
        }
        
        return true;
    }
    
    startCrafting(recipeId) {
        if (!this.canCraft(recipeId)) {
            console.log(`Cannot craft ${recipeId}`);
            return false;
        }
        
        const recipe = this.recipes[recipeId];
        const player = this.game.characterEngine.getPlayer();
        
        // Consume ingredients
        for (const ingredient of recipe.ingredients) {
            player.inventory.removeItem(ingredient.id, ingredient.quantity);
        }
        
        // Start crafting process
        this.currentCrafting = {
            recipe: recipe,
            timeRemaining: recipe.craftingTime,
            totalTime: recipe.craftingTime
        };
        
        console.log(`Started crafting: ${recipe.name} (${recipe.craftingTime}s)`);
        return true;
    }
    
    update(deltaTime) {
        // Update current crafting
        if (this.currentCrafting) {
            this.currentCrafting.timeRemaining -= deltaTime;
            
            if (this.currentCrafting.timeRemaining <= 0) {
                this.completeCrafting();
            }
        }
        
        // Process crafting queue
        if (!this.currentCrafting && this.craftingQueue.length > 0) {
            const nextRecipe = this.craftingQueue.shift();
            this.startCrafting(nextRecipe);
        }
    }
    
    completeCrafting() {
        if (!this.currentCrafting) return;
        
        const recipe = this.currentCrafting.recipe;
        const player = this.game.characterEngine.getPlayer();
        
        // Add result to inventory
        player.inventory.addItem(recipe.result.id, recipe.result.quantity);
        
        // Award science points
        if (recipe.sciencePoints) {
            this.game.gameManager.gameProgress.sciencePoints += recipe.sciencePoints;
            console.log(`Gained ${recipe.sciencePoints} science points!`);
        }
        
        console.log(`Crafted: ${recipe.name}`);
        
        // Notify game manager
        this.game.gameManager.emit('itemCrafted', recipe.result.id);
        
        // Check for station unlocks
        this.checkStationUnlocks(recipe.result.id);
        
        this.currentCrafting = null;
    }
    
    checkStationUnlocks(itemId) {
        for (const [stationId, station] of Object.entries(this.stationDefinitions)) {
            if (!station.available && station.unlockRecipe === itemId) {
                station.available = true;
                console.log(`Crafting station unlocked: ${station.name}`);
            }
        }
    }
    
    unlockTechNode(nodeId) {
        const node = this.techTreeNodes.get(nodeId);
        if (!node) return false;
        
        // Check prerequisites
        if (node.prerequisites) {
            for (const prereq of node.prerequisites) {
                const prereqNode = this.techTreeNodes.get(prereq);
                if (!prereqNode || !prereqNode.unlocked) {
                    console.log(`Prerequisite not met: ${prereq}`);
                    return false;
                }
            }
        }
        
        // Check science point cost
        const currentPoints = this.game.gameManager.getSciencePoints();
        if (currentPoints < node.sciencePointCost) {
            console.log(`Not enough science points. Need ${node.sciencePointCost}, have ${currentPoints}`);
            return false;
        }
        
        // Spend science points
        this.game.gameManager.spendSciencePoints(node.sciencePointCost);
        
        // Unlock node
        node.unlocked = true;
        
        // Unlock recipes
        for (const recipeId of node.recipes) {
            this.unlockRecipe(recipeId);
        }
        
        console.log(`Tech node unlocked: ${node.name}`);
        return true;
    }
    
    unlockRecipe(recipeId) {
        if (!this.unlockedRecipes.has(recipeId)) {
            this.unlockedRecipes.add(recipeId);
            console.log(`Recipe unlocked: ${this.recipes[recipeId]?.name || recipeId}`);
            
            // Notify game manager
            this.game.gameManager.emit('recipeDiscovered', recipeId);
        }
    }
    
    getAvailableRecipes() {
        const available = [];
        for (const recipeId of this.unlockedRecipes) {
            const recipe = this.recipes[recipeId];
            if (recipe) {
                available.push({
                    ...recipe,
                    canCraft: this.canCraft(recipeId)
                });
            }
        }
        return available;
    }
    
    getCraftingProgress() {
        if (!this.currentCrafting) return null;
        
        const progress = 1 - (this.currentCrafting.timeRemaining / this.currentCrafting.totalTime);
        return {
            recipe: this.currentCrafting.recipe,
            progress: Math.max(0, Math.min(1, progress)),
            timeRemaining: this.currentCrafting.timeRemaining
        };
    }
    
    // UI Methods
    showCraftingInterface() {
        console.log('=== Crafting Interface ===');
        
        const available = this.getAvailableRecipes();
        
        console.log('Available Recipes:');
        for (const recipe of available) {
            const status = recipe.canCraft ? '[✓]' : '[✗]';
            console.log(`${status} ${recipe.name} - ${recipe.description}`);
            
            console.log('  Ingredients:');
            for (const ingredient of recipe.ingredients) {
                console.log(`    - ${ingredient.quantity}x ${ingredient.id}`);
            }
            console.log(`  Station: ${recipe.station}`);
            console.log(`  Time: ${recipe.craftingTime}s`);
            console.log('');
        }
        
        // Show current crafting
        const progress = this.getCraftingProgress();
        if (progress) {
            console.log(`Currently crafting: ${progress.recipe.name}`);
            console.log(`Progress: ${Math.round(progress.progress * 100)}%`);
            console.log(`Time remaining: ${Math.round(progress.timeRemaining)}s`);
        }
    }
    
    showTechTree() {
        console.log('=== Technology Tree ===');
        
        for (const [nodeId, node] of this.techTreeNodes) {
            const status = node.unlocked ? '[UNLOCKED]' : `[${node.sciencePointCost} SP]`;
            console.log(`${status} ${node.name} - ${node.description}`);
            
            if (node.unlocked) {
                console.log('  Recipes:');
                for (const recipeId of node.recipes) {
                    const recipe = this.recipes[recipeId];
                    if (recipe) {
                        console.log(`    - ${recipe.name}`);
                    }
                }
            }
            console.log('');
        }
        
        const currentPoints = this.game.gameManager.getSciencePoints();
        console.log(`Current Science Points: ${currentPoints}`);
    }
    
    render(renderer) {
        // Render crafting progress if active
        const progress = this.getCraftingProgress();
        if (progress) {
            this.renderCraftingProgress(renderer, progress);
        }
    }
    
    renderCraftingProgress(renderer, progress) {
        const canvas = renderer.ctx.canvas;
        const barWidth = 200;
        const barHeight = 20;
        const x = (canvas.width - barWidth) / 2;
        const y = 100;
        
        // Background
        renderer.drawUIRect(x, y, barWidth, barHeight, '#333', true);
        
        // Progress fill
        const fillWidth = barWidth * progress.progress;
        renderer.drawUIRect(x, y, fillWidth, barHeight, '#4CAF50', true);
        
        // Border
        renderer.drawUIRect(x, y, barWidth, barHeight, '#666', false);
        
        // Text
        renderer.drawUIText(`Crafting: ${progress.recipe.name}`, x, y - 25, {
            font: '12px monospace',
            color: 'white'
        });
        
        const timeText = `${Math.ceil(progress.timeRemaining)}s remaining`;
        renderer.drawUIText(timeText, x + barWidth - 80, y - 25, {
            font: '10px monospace',
            color: '#ccc'
        });
    }
    
    // Save/Load
    getSaveData() {
        return {
            unlockedRecipes: Array.from(this.unlockedRecipes),
            techTreeNodes: Array.from(this.techTreeNodes.entries()),
            currentCrafting: this.currentCrafting,
            craftingQueue: this.craftingQueue
        };
    }
    
    loadSaveData(data) {
        this.unlockedRecipes = new Set(data.unlockedRecipes);
        this.techTreeNodes = new Map(data.techTreeNodes);
        this.currentCrafting = data.currentCrafting;
        this.craftingQueue = data.craftingQueue || [];
    }
}
