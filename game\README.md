# Dr. Stone: Pixel Kingdom

A 2D pixel art RPG based on the Dr. Stone anime/manga series, featuring crafting, exploration, and scientific discovery.

## Game Features

### Core Gameplay
- **Exploration**: Navigate through different areas including forests, caves, and villages
- **Crafting System**: Create tools, weapons, and scientific inventions using gathered resources
- **Tech Tree**: Unlock new technologies and recipes by spending science points
- **Character Progression**: Level up characters and improve their stats
- **Story Mode**: Follow the main story arcs from the Dr. Stone series
- **Turn-based Combat**: Strategic battles with enemies

### Characters
- **Senku Ishigami**: The brilliant scientist protagonist
- **Kohaku**: Fierce warrior with lioness strength
- **Chrome**: Curious sorcerer learning about science
- **Taiju**: Strong gatherer and loyal friend
- **Yu<PERSON><PERSON>ha**: Skilled crafter and support character
- **<PERSON><PERSON>**: Scout with stealth abilities

### Areas
- **Starting Forest**: Where <PERSON><PERSON> first awakens
- **Cave of Miracles**: Source of the revival fluid
- **Ishigami Village**: Primitive village of survivors

## Controls

### Movement
- **Arrow Keys** or **WASD**: Move character
- **Space** or **E**: Interact with objects
- **Mouse Click**: Click to move or interact

### UI Controls
- **I**: Open inventory
- **C**: Open crafting interface
- **T**: Open tech tree
- **M** or **Escape**: Open main menu
- **1-4**: Use quick slot items

### Debug Controls
- **F1**: Toggle debug mode
- **F2**: Show debug information

## Game Systems

### Crafting
1. Gather resources from the environment
2. Unlock recipes through story progression or discovery
3. Use appropriate crafting stations
4. Spend time crafting items
5. Gain science points for successful crafting

### Tech Tree
- Spend science points to unlock new technology nodes
- Each node unlocks new recipes and capabilities
- Progress from Stone Age to advanced science

### Combat
- Turn-based tactical combat
- Choose from Attack, Defend, Use Item, or Run
- Gain experience and loot from victories
- Strategic use of character abilities

### Story Progression
- Complete quests to advance the story
- Unlock new areas and characters
- Make scientific discoveries
- Build the Kingdom of Science

## Installation & Running

1. Open `index.html` in a modern web browser
2. The game will load automatically
3. Use keyboard and mouse to play

## Technical Details

- Built with HTML5 Canvas and JavaScript
- Pixel-perfect rendering for retro aesthetic
- Modular engine architecture
- Save/load system using localStorage
- Responsive design for different screen sizes

## Development Status

This is a prototype implementation featuring:
- ✅ Core game architecture
- ✅ Basic movement and interaction
- ✅ Crafting system foundation
- ✅ Story engine with quests
- ✅ Character management
- ✅ World exploration
- ✅ Turn-based combat
- ⏳ Visual assets (using placeholders)
- ⏳ Audio system
- ⏳ Advanced UI polish
- ⏳ Complete story content

## Future Enhancements

- Full sprite artwork for all characters and objects
- Sound effects and background music
- More complex crafting mini-games
- Expanded story content covering all manga arcs
- Multiplayer cooperation mode
- Mobile touch controls
- Achievement system
- Mod support

## Credits

Based on the Dr. Stone manga by Riichiro Inagaki and Boichi.
Game implementation created as a tribute to the series.

---

**"This is exhilarating! 10 billion percent!"** - Senku Ishigami
