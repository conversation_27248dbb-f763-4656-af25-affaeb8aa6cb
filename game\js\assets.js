/**
 * Asset Manager - Handles loading and managing game assets
 */

class AssetManager {
    constructor() {
        this.assets = new Map();
        this.loadingPromises = new Map();
        this.loadedCount = 0;
        this.totalCount = 0;
        
        // Asset definitions
        this.assetDefinitions = {
            // Character sprites (32x32)
            characters: {
                'senku': 'assets/characters/senku_sprite.png',
                'kohaku': 'assets/characters/kohaku_sprite.png',
                'chrome': 'assets/characters/chrome_sprite.png',
                'taiju': 'assets/characters/taiju_sprite.png',
                'yuzuriha': 'assets/characters/yuzuriha_sprite.png',
                'suika': 'assets/characters/suika_sprite.png'
            },
            
            // Environment tiles (16x16)
            tiles: {
                'grass': 'assets/tiles/grass.png',
                'stone': 'assets/tiles/stone.png',
                'water': 'assets/tiles/water.png',
                'tree': 'assets/tiles/tree.png',
                'cave_wall': 'assets/tiles/cave_wall.png',
                'village_ground': 'assets/tiles/village_ground.png'
            },
            
            // UI elements
            ui: {
                'button': 'assets/ui/button.png',
                'panel': 'assets/ui/panel.png',
                'inventory_slot': 'assets/ui/inventory_slot.png',
                'health_bar': 'assets/ui/health_bar.png',
                'progress_bar': 'assets/ui/progress_bar.png'
            },
            
            // Items and objects
            items: {
                'stone_item': 'assets/items/stone.png',
                'wood_item': 'assets/items/wood.png',
                'stone_axe': 'assets/items/stone_axe.png',
                'wooden_spear': 'assets/items/wooden_spear.png',
                'revival_fluid': 'assets/items/revival_fluid.png',
                'beaker': 'assets/items/beaker.png'
            },
            
            // Effects and particles
            effects: {
                'sparkle': 'assets/effects/sparkle.png',
                'smoke': 'assets/effects/smoke.png',
                'explosion': 'assets/effects/explosion.png'
            }
        };
    }
    
    async loadAll() {
        console.log('Loading all game assets...');
        
        // Count total assets
        this.totalCount = this.countAssets();
        this.loadedCount = 0;
        
        // Create placeholder assets first
        this.createPlaceholderAssets();
        
        // Load all asset categories
        const loadPromises = [];
        
        for (const [category, assets] of Object.entries(this.assetDefinitions)) {
            for (const [name, path] of Object.entries(assets)) {
                loadPromises.push(this.loadAsset(category, name, path));
            }
        }
        
        // Wait for all assets to load (or fail gracefully)
        await Promise.allSettled(loadPromises);
        
        console.log(`Assets loaded: ${this.loadedCount}/${this.totalCount}`);
    }
    
    countAssets() {
        let count = 0;
        for (const assets of Object.values(this.assetDefinitions)) {
            count += Object.keys(assets).length;
        }
        return count;
    }
    
    createPlaceholderAssets() {
        // Create colored placeholder canvases for missing assets
        const placeholders = {
            // Character placeholders (32x32)
            'senku': this.createPlaceholder(32, 32, '#4CAF50'), // Green for Senku
            'kohaku': this.createPlaceholder(32, 32, '#FFC107'), // Yellow for Kohaku
            'chrome': this.createPlaceholder(32, 32, '#8D6E63'), // Brown for Chrome
            'taiju': this.createPlaceholder(32, 32, '#2196F3'), // Blue for Taiju
            'yuzuriha': this.createPlaceholder(32, 32, '#E91E63'), // Pink for Yuzuriha
            'suika': this.createPlaceholder(32, 32, '#FF5722'), // Orange for Suika
            
            // Tile placeholders (16x16)
            'grass': this.createPlaceholder(16, 16, '#4CAF50'),
            'stone': this.createPlaceholder(16, 16, '#9E9E9E'),
            'water': this.createPlaceholder(16, 16, '#2196F3'),
            'tree': this.createPlaceholder(16, 16, '#795548'),
            'cave_wall': this.createPlaceholder(16, 16, '#424242'),
            'village_ground': this.createPlaceholder(16, 16, '#8D6E63'),
            
            // UI placeholders
            'button': this.createPlaceholder(64, 24, '#616161'),
            'panel': this.createPlaceholder(100, 100, '#424242'),
            'inventory_slot': this.createPlaceholder(32, 32, '#757575'),
            'health_bar': this.createPlaceholder(100, 8, '#F44336'),
            'progress_bar': this.createPlaceholder(100, 8, '#4CAF50'),
            
            // Item placeholders
            'stone_item': this.createPlaceholder(16, 16, '#9E9E9E'),
            'wood_item': this.createPlaceholder(16, 16, '#795548'),
            'stone_axe': this.createPlaceholder(16, 16, '#607D8B'),
            'wooden_spear': this.createPlaceholder(16, 16, '#8D6E63'),
            'revival_fluid': this.createPlaceholder(16, 16, '#00BCD4'),
            'beaker': this.createPlaceholder(16, 16, '#E0E0E0'),
            
            // Effect placeholders
            'sparkle': this.createPlaceholder(8, 8, '#FFEB3B'),
            'smoke': this.createPlaceholder(16, 16, '#9E9E9E'),
            'explosion': this.createPlaceholder(24, 24, '#FF5722')
        };
        
        // Store placeholders
        for (const [name, canvas] of Object.entries(placeholders)) {
            this.assets.set(name, canvas);
        }
    }
    
    createPlaceholder(width, height, color) {
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        
        // Fill with color
        ctx.fillStyle = color;
        ctx.fillRect(0, 0, width, height);
        
        // Add border
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 1;
        ctx.strokeRect(0, 0, width, height);
        
        // Add simple pattern for identification
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fillRect(2, 2, width - 4, 2);
        ctx.fillRect(2, 2, 2, height - 4);
        
        return canvas;
    }
    
    async loadAsset(category, name, path) {
        try {
            const image = new Image();
            
            const loadPromise = new Promise((resolve, reject) => {
                image.onload = () => {
                    this.assets.set(name, image);
                    this.loadedCount++;
                    console.log(`Loaded asset: ${name} (${this.loadedCount}/${this.totalCount})`);
                    resolve(image);
                };
                
                image.onerror = () => {
                    console.warn(`Failed to load asset: ${path}, using placeholder`);
                    this.loadedCount++;
                    resolve(null); // Keep placeholder
                };
            });
            
            image.src = path;
            return await loadPromise;
            
        } catch (error) {
            console.warn(`Error loading asset ${name}:`, error);
            this.loadedCount++;
            return null; // Keep placeholder
        }
    }
    
    get(name) {
        return this.assets.get(name);
    }
    
    has(name) {
        return this.assets.has(name);
    }
    
    getCharacter(name) {
        return this.get(name);
    }
    
    getTile(name) {
        return this.get(name);
    }
    
    getUI(name) {
        return this.get(name);
    }
    
    getItem(name) {
        return this.get(name);
    }
    
    getEffect(name) {
        return this.get(name);
    }
    
    // Sprite sheet utilities
    createSpriteSheet(image, spriteWidth, spriteHeight) {
        const sprites = [];
        const cols = Math.floor(image.width / spriteWidth);
        const rows = Math.floor(image.height / spriteHeight);
        
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                const canvas = document.createElement('canvas');
                canvas.width = spriteWidth;
                canvas.height = spriteHeight;
                
                const ctx = canvas.getContext('2d');
                ctx.drawImage(
                    image,
                    col * spriteWidth, row * spriteHeight,
                    spriteWidth, spriteHeight,
                    0, 0,
                    spriteWidth, spriteHeight
                );
                
                sprites.push(canvas);
            }
        }
        
        return sprites;
    }
    
    getLoadingProgress() {
        return this.totalCount > 0 ? this.loadedCount / this.totalCount : 1;
    }
    
    isLoaded() {
        return this.loadedCount >= this.totalCount;
    }
}
