/**
 * Story Engine - Manages narrative, quests, and dialogue
 */

class StoryEngine {
    constructor(game) {
        this.game = game;
        
        // Story state
        this.currentChapter = null;
        this.activeQuests = new Map();
        this.completedQuests = new Set();
        this.storyFlags = new Map();
        
        // Dialogue system
        this.currentDialogue = null;
        this.dialogueHistory = [];
        
        // Chapter definitions
        this.chapters = {
            stone_formula: {
                name: 'The Stone Formula',
                description: '<PERSON><PERSON> awakens in a world of stone and begins his scientific journey.',
                quests: ['awakening', 'first_tools', 'create_revival_fluid'],
                unlocks: ['cave_of_miracles'],
                objectives: [
                    'Gather basic resources',
                    'Create stone tools',
                    'Discover the revival fluid formula',
                    'Collect revival fluid from the cave'
                ]
            },
            kingdom_of_science: {
                name: 'Kingdom of Science',
                description: '<PERSON><PERSON> encounters the village and begins building his kingdom.',
                quests: ['meet_village', 'prove_science', 'unite_village'],
                unlocks: ['ishigami_village'],
                objectives: [
                    'Find Ishigami Village',
                    'Meet Kohaku and Chrome',
                    'Demonstrate scientific inventions',
                    'Unite the village under science'
                ]
            }
        };
        
        // Quest definitions
        this.questDefinitions = {
            awakening: {
                id: 'awakening',
                name: 'Awakening',
                description: '<PERSON><PERSON> has awakened in a world of stone. Explore and gather information.',
                objectives: [
                    { id: 'explore_area', description: 'Explore the starting area', completed: false },
                    { id: 'gather_stone', description: 'Gather 5 stones', completed: false, target: 5 }
                ],
                rewards: { sciencePoints: 10, items: [] },
                autoStart: true
            },
            first_tools: {
                id: 'first_tools',
                name: 'First Tools',
                description: 'Create your first stone tools to survive in this world.',
                objectives: [
                    { id: 'craft_stone_axe', description: 'Craft a stone axe', completed: false },
                    { id: 'gather_wood', description: 'Gather 3 wood using the axe', completed: false, target: 3 }
                ],
                rewards: { sciencePoints: 15, items: ['wooden_spear'] },
                prerequisites: ['awakening']
            },
            create_revival_fluid: {
                id: 'create_revival_fluid',
                name: 'The Revival Formula',
                description: 'Discover and create the miraculous revival fluid.',
                objectives: [
                    { id: 'find_cave', description: 'Find the Cave of Miracles', completed: false },
                    { id: 'collect_revival_fluid', description: 'Collect revival fluid', completed: false }
                ],
                rewards: { sciencePoints: 50, items: ['revival_fluid'] },
                prerequisites: ['first_tools']
            }
        };
        
        // Dialogue definitions
        this.dialogues = {
            senku_awakening: {
                speaker: 'Senku',
                text: "This is exhilarating! I've been conscious for... 3,700 years! But now I'm free, and I'm going to rebuild civilization with the power of science!",
                choices: [
                    { text: "Let's get started!", action: 'continue' }
                ]
            },
            first_stone: {
                speaker: 'Senku',
                text: "Stone... the foundation of all technology. With this, I can create tools, and with tools, I can create everything else!",
                choices: [
                    { text: "Gather more stones", action: 'continue' }
                ]
            }
        };
    }
    
    async init() {
        console.log('Initializing Story Engine...');
        
        // Set up event listeners
        this.setupEventListeners();
        
        console.log('Story Engine initialized');
    }
    
    setupEventListeners() {
        // Listen for game events that affect story
        this.game.gameManager.on('revivalFluidCollected', () => {
            this.completeObjective('create_revival_fluid', 'collect_revival_fluid');
        });
        
        this.game.gameManager.on('itemCrafted', (item) => {
            if (item === 'stone_axe') {
                this.completeObjective('first_tools', 'craft_stone_axe');
            }
        });
        
        this.game.gameManager.on('itemCollected', (item, quantity) => {
            this.checkCollectionObjectives(item, quantity);
        });
    }
    
    startChapter(chapterId) {
        const chapter = this.chapters[chapterId];
        if (!chapter) {
            console.error(`Chapter not found: ${chapterId}`);
            return;
        }
        
        this.currentChapter = chapterId;
        console.log(`Starting chapter: ${chapter.name}`);
        
        // Start chapter quests
        for (const questId of chapter.quests) {
            const quest = this.questDefinitions[questId];
            if (quest && quest.autoStart) {
                this.startQuest(questId);
            }
        }
        
        // Unlock areas
        for (const areaId of chapter.unlocks || []) {
            this.game.gameManager.emit('areaUnlocked', areaId);
        }
        
        // Show chapter introduction
        this.showChapterIntro(chapter);
    }
    
    showChapterIntro(chapter) {
        console.log(`=== ${chapter.name} ===`);
        console.log(chapter.description);
        
        // Show objectives
        console.log('Objectives:');
        for (const objective of chapter.objectives) {
            console.log(`- ${objective}`);
        }
    }
    
    startQuest(questId) {
        const questDef = this.questDefinitions[questId];
        if (!questDef) {
            console.error(`Quest definition not found: ${questId}`);
            return;
        }
        
        // Check prerequisites
        if (questDef.prerequisites) {
            for (const prereq of questDef.prerequisites) {
                if (!this.completedQuests.has(prereq)) {
                    console.log(`Quest ${questId} requires ${prereq} to be completed first`);
                    return;
                }
            }
        }
        
        // Create quest instance
        const quest = new Quest(questDef);
        this.activeQuests.set(questId, quest);
        
        console.log(`Quest started: ${quest.name}`);
        console.log(quest.description);
        
        // Show objectives
        for (const objective of quest.objectives) {
            console.log(`- ${objective.description}`);
        }
        
        // Trigger quest start dialogue if exists
        if (questId === 'awakening') {
            this.showDialogue('senku_awakening');
        }
    }
    
    completeObjective(questId, objectiveId) {
        const quest = this.activeQuests.get(questId);
        if (!quest) return;
        
        const objective = quest.objectives.find(obj => obj.id === objectiveId);
        if (objective && !objective.completed) {
            objective.completed = true;
            console.log(`Objective completed: ${objective.description}`);
            
            // Check if quest is complete
            if (quest.isComplete()) {
                this.completeQuest(questId);
            }
        }
    }
    
    checkCollectionObjectives(itemId, quantity) {
        for (const [questId, quest] of this.activeQuests) {
            for (const objective of quest.objectives) {
                if (objective.id === 'gather_stone' && itemId === 'stone') {
                    objective.current = (objective.current || 0) + quantity;
                    if (objective.current >= objective.target) {
                        this.completeObjective(questId, objective.id);
                    }
                } else if (objective.id === 'gather_wood' && itemId === 'wood') {
                    objective.current = (objective.current || 0) + quantity;
                    if (objective.current >= objective.target) {
                        this.completeObjective(questId, objective.id);
                    }
                }
            }
        }
    }
    
    completeQuest(questId) {
        const quest = this.activeQuests.get(questId);
        if (!quest) return;
        
        console.log(`Quest completed: ${quest.name}`);
        
        // Give rewards
        const player = this.game.characterEngine.getPlayer();
        if (player && quest.rewards) {
            if (quest.rewards.sciencePoints) {
                this.game.gameManager.gameProgress.sciencePoints += quest.rewards.sciencePoints;
                console.log(`Gained ${quest.rewards.sciencePoints} science points!`);
            }
            
            if (quest.rewards.items) {
                for (const item of quest.rewards.items) {
                    player.inventory.addItem(item, 1);
                    console.log(`Received: ${item}`);
                }
            }
        }
        
        // Move quest to completed
        this.activeQuests.delete(questId);
        this.completedQuests.add(questId);
        
        // Notify game manager
        this.game.gameManager.emit('questCompleted', quest);
        
        // Start next quests
        this.checkForNewQuests();
    }
    
    checkForNewQuests() {
        for (const [questId, questDef] of Object.entries(this.questDefinitions)) {
            if (!this.activeQuests.has(questId) && !this.completedQuests.has(questId)) {
                // Check if prerequisites are met
                let canStart = true;
                if (questDef.prerequisites) {
                    for (const prereq of questDef.prerequisites) {
                        if (!this.completedQuests.has(prereq)) {
                            canStart = false;
                            break;
                        }
                    }
                }
                
                if (canStart) {
                    this.startQuest(questId);
                }
            }
        }
    }
    
    showDialogue(dialogueId) {
        const dialogue = this.dialogues[dialogueId];
        if (!dialogue) {
            console.error(`Dialogue not found: ${dialogueId}`);
            return;
        }
        
        this.currentDialogue = dialogue;
        console.log(`${dialogue.speaker}: "${dialogue.text}"`);
        
        // Show choices
        if (dialogue.choices) {
            for (let i = 0; i < dialogue.choices.length; i++) {
                console.log(`${i + 1}. ${dialogue.choices[i].text}`);
            }
        }
    }
    
    selectDialogueChoice(choiceIndex) {
        if (!this.currentDialogue || !this.currentDialogue.choices) return;
        
        const choice = this.currentDialogue.choices[choiceIndex];
        if (choice) {
            console.log(`Selected: ${choice.text}`);
            
            // Handle choice action
            if (choice.action === 'continue') {
                this.currentDialogue = null;
            }
            // Add more choice actions as needed
        }
    }
    
    update(deltaTime) {
        // Update active quests
        for (const quest of this.activeQuests.values()) {
            quest.update(deltaTime);
        }
    }
    
    render(renderer) {
        // Render dialogue if active
        if (this.currentDialogue) {
            this.renderDialogue(renderer);
        }
        
        // Render quest notifications
        this.renderQuestNotifications(renderer);
    }
    
    renderDialogue(renderer) {
        const dialogue = this.currentDialogue;
        const canvas = renderer.ctx.canvas;
        
        // Dialogue box
        const boxHeight = 120;
        const boxY = canvas.height - boxHeight - 20;
        
        renderer.drawUIRect(20, boxY, canvas.width - 40, boxHeight, 'rgba(0, 0, 0, 0.8)', true);
        renderer.drawUIRect(20, boxY, canvas.width - 40, boxHeight, '#666', false);
        
        // Speaker name
        renderer.drawUIText(dialogue.speaker, 30, boxY + 10, {
            font: 'bold 14px monospace',
            color: '#4CAF50'
        });
        
        // Dialogue text
        renderer.drawUIText(dialogue.text, 30, boxY + 35, {
            font: '12px monospace',
            color: 'white'
        });
        
        // Choices
        if (dialogue.choices) {
            let choiceY = boxY + 70;
            for (let i = 0; i < dialogue.choices.length; i++) {
                renderer.drawUIText(`${i + 1}. ${dialogue.choices[i].text}`, 40, choiceY, {
                    font: '11px monospace',
                    color: '#FFEB3B'
                });
                choiceY += 20;
            }
        }
    }
    
    renderQuestNotifications(renderer) {
        // Show active quest objectives in top-right corner
        let y = 80;
        for (const quest of this.activeQuests.values()) {
            renderer.drawUIText(quest.name, renderer.ctx.canvas.width - 250, y, {
                font: 'bold 12px monospace',
                color: '#4CAF50'
            });
            y += 20;
            
            for (const objective of quest.objectives) {
                if (!objective.completed) {
                    let text = objective.description;
                    if (objective.target) {
                        const current = objective.current || 0;
                        text += ` (${current}/${objective.target})`;
                    }
                    
                    renderer.drawUIText(`- ${text}`, renderer.ctx.canvas.width - 240, y, {
                        font: '10px monospace',
                        color: 'white'
                    });
                    y += 15;
                }
            }
            y += 10;
        }
    }
    
    // UI Methods
    showQuestLog() {
        console.log('=== Quest Log ===');
        
        console.log('Active Quests:');
        for (const quest of this.activeQuests.values()) {
            console.log(`- ${quest.name}: ${quest.description}`);
            for (const objective of quest.objectives) {
                const status = objective.completed ? '[✓]' : '[ ]';
                console.log(`  ${status} ${objective.description}`);
            }
        }
        
        console.log('Completed Quests:');
        for (const questId of this.completedQuests) {
            const questDef = this.questDefinitions[questId];
            if (questDef) {
                console.log(`- ${questDef.name}`);
            }
        }
    }
    
    // Save/Load
    getSaveData() {
        return {
            currentChapter: this.currentChapter,
            activeQuests: Array.from(this.activeQuests.entries()).map(([id, quest]) => ({
                id,
                data: quest.getSaveData()
            })),
            completedQuests: Array.from(this.completedQuests),
            storyFlags: Array.from(this.storyFlags.entries())
        };
    }
    
    loadSaveData(data) {
        this.currentChapter = data.currentChapter;
        
        this.activeQuests.clear();
        for (const questEntry of data.activeQuests) {
            const quest = Quest.fromSaveData(questEntry.data);
            this.activeQuests.set(questEntry.id, quest);
        }
        
        this.completedQuests = new Set(data.completedQuests);
        this.storyFlags = new Map(data.storyFlags);
    }
}

/**
 * Quest class
 */
class Quest {
    constructor(definition) {
        this.id = definition.id;
        this.name = definition.name;
        this.description = definition.description;
        this.objectives = [...definition.objectives];
        this.rewards = definition.rewards;
        this.startTime = Date.now();
    }
    
    isComplete() {
        return this.objectives.every(obj => obj.completed);
    }
    
    update(deltaTime) {
        // Update quest logic
    }
    
    getSaveData() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            objectives: this.objectives,
            rewards: this.rewards,
            startTime: this.startTime
        };
    }
    
    static fromSaveData(data) {
        const quest = new Quest(data);
        quest.startTime = data.startTime;
        return quest;
    }
}
