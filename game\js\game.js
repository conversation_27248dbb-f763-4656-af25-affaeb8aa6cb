/**
 * Dr. Stone: Pixel Kingdom - Main Game Class
 * This is the core game class that orchestrates all game systems
 */

class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Game state
        this.isRunning = false;
        this.isPaused = false;
        this.gameState = 'loading'; // loading, menu, playing, paused
        
        // Game systems
        this.gameManager = null;
        this.storyEngine = null;
        this.craftingEngine = null;
        this.characterEngine = null;
        this.worldEngine = null;
        this.combatEngine = null;
        this.renderer = null;
        this.input = null;
        this.assets = null;
        
        // Game settings
        this.settings = {
            resolution: { width: 960, height: 540 },
            pixelScale: 2,
            targetFPS: 60
        };
        
        // Game loop
        this.lastTime = 0;
        this.deltaTime = 0;
        this.frameCount = 0;
        
        // UI elements
        this.ui = {
            loading: document.getElementById('loading'),
            menu: document.getElementById('menu'),
            hud: document.getElementById('hud')
        };
    }
    
    async init() {
        console.log('Initializing Dr. Stone: Pixel Kingdom...');
        
        try {
            // Initialize core systems
            await this.initializeSystems();
            
            // Load game assets
            await this.loadAssets();
            
            // Set up input handling
            this.setupInput();
            
            // Start the game
            this.start();
            
            console.log('Game initialized successfully!');
        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.showError('Failed to load game. Please refresh the page.');
        }
    }
    
    async initializeSystems() {
        // Initialize all game engines
        this.assets = new AssetManager();
        this.input = new InputManager(this);
        this.renderer = new Renderer(this.ctx, this.settings);
        
        this.gameManager = new GameManager(this);
        this.storyEngine = new StoryEngine(this);
        this.craftingEngine = new CraftingEngine(this);
        this.characterEngine = new CharacterEngine(this);
        this.worldEngine = new WorldEngine(this);
        this.combatEngine = new CombatEngine(this);
        
        // Initialize each system
        await this.gameManager.init();
        await this.storyEngine.init();
        await this.craftingEngine.init();
        await this.characterEngine.init();
        await this.worldEngine.init();
        await this.combatEngine.init();
        await this.renderer.init();
    }
    
    async loadAssets() {
        console.log('Loading game assets...');
        
        // Load all game assets
        await this.assets.loadAll();
        
        console.log('Assets loaded successfully!');
    }
    
    setupInput() {
        // Set up keyboard and mouse input
        this.input.init();
    }
    
    start() {
        this.isRunning = true;
        this.gameState = 'playing';
        this.ui.loading.style.display = 'none';
        
        // Start the game loop
        this.gameLoop();
        
        // Initialize the first chapter
        this.storyEngine.startChapter('stone_formula');
    }
    
    gameLoop(currentTime = 0) {
        if (!this.isRunning) return;
        
        // Calculate delta time
        this.deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        this.frameCount++;
        
        // Update game systems
        this.update(this.deltaTime);
        
        // Render the game
        this.render();
        
        // Continue the loop
        requestAnimationFrame((time) => this.gameLoop(time));
    }
    
    update(deltaTime) {
        if (this.isPaused || this.gameState !== 'playing') return;
        
        // Update all game systems
        this.input.update(deltaTime);
        this.gameManager.update(deltaTime);
        this.storyEngine.update(deltaTime);
        this.characterEngine.update(deltaTime);
        this.worldEngine.update(deltaTime);
        this.combatEngine.update(deltaTime);
        this.craftingEngine.update(deltaTime);
        
        // Update UI
        this.updateUI();
    }
    
    render() {
        // Clear the canvas
        this.ctx.fillStyle = '#2d5016'; // Forest green background
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Render all game systems
        this.worldEngine.render(this.renderer);
        this.characterEngine.render(this.renderer);
        this.combatEngine.render(this.renderer);
        this.storyEngine.render(this.renderer);
        
        // Render debug info if needed
        if (this.settings.showDebug) {
            this.renderDebugInfo();
        }
    }
    
    updateUI() {
        // Update character stats in HUD
        const player = this.characterEngine.getPlayer();
        if (player) {
            document.getElementById('hp').textContent = player.stats.hp;
            document.getElementById('sp').textContent = player.stats.sp;
            document.getElementById('stamina').textContent = Math.round(player.stats.stamina);
            document.getElementById('hunger').textContent = Math.round(player.stats.hunger);
            document.getElementById('thirst').textContent = Math.round(player.stats.thirst);
        }
    }
    
    renderDebugInfo() {
        this.ctx.fillStyle = 'white';
        this.ctx.font = '12px monospace';
        this.ctx.fillText(`FPS: ${Math.round(1 / this.deltaTime)}`, 10, 20);
        this.ctx.fillText(`Frame: ${this.frameCount}`, 10, 35);
        this.ctx.fillText(`State: ${this.gameState}`, 10, 50);
    }
    
    // UI Methods
    showInventory() {
        console.log('Opening inventory...');
        this.characterEngine.showInventory();
    }
    
    showCrafting() {
        console.log('Opening crafting interface...');
        this.craftingEngine.showCraftingInterface();
    }
    
    showTechTree() {
        console.log('Opening tech tree...');
        this.craftingEngine.showTechTree();
    }
    
    showParty() {
        console.log('Opening party management...');
        this.characterEngine.showPartyManagement();
    }
    
    showQuests() {
        console.log('Opening quest log...');
        this.storyEngine.showQuestLog();
    }
    
    hideMenu() {
        this.ui.menu.style.display = 'none';
    }
    
    saveGame() {
        console.log('Saving game...');
        const saveData = this.gameManager.createSaveData();
        localStorage.setItem('drstone_save', JSON.stringify(saveData));
        alert('Game saved successfully!');
        this.hideMenu();
    }
    
    loadGame() {
        console.log('Loading game...');
        const saveData = localStorage.getItem('drstone_save');
        if (saveData) {
            this.gameManager.loadSaveData(JSON.parse(saveData));
            alert('Game loaded successfully!');
        } else {
            alert('No save data found!');
        }
        this.hideMenu();
    }
    
    pause() {
        this.isPaused = true;
        this.gameState = 'paused';
    }
    
    resume() {
        this.isPaused = false;
        this.gameState = 'playing';
    }
    
    stop() {
        this.isRunning = false;
    }
    
    showError(message) {
        this.ui.loading.textContent = `Error: ${message}`;
        this.ui.loading.style.color = '#ff4444';
    }
}
