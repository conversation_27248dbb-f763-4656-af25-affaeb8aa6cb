/**
 * <PERSON><PERSON><PERSON> - <PERSON>les all drawing operations with pixel-perfect rendering
 */

class Renderer {
    constructor(ctx, settings) {
        this.ctx = ctx;
        this.settings = settings;
        
        // Camera/viewport
        this.camera = {
            x: 0,
            y: 0,
            zoom: 1,
            targetX: 0,
            targetY: 0,
            smoothing: 0.1
        };
        
        // Rendering settings
        this.pixelScale = settings.pixelScale || 2;
        this.tileSize = 16;
        this.spriteSize = 32;
        
        // Layer system
        this.layers = {
            background: 0,
            terrain: 1,
            objects: 2,
            characters: 3,
            effects: 4,
            ui: 5
        };
        
        // Render queue
        this.renderQueue = [];
        
        // Debug rendering
        this.debugMode = false;
    }
    
    async init() {
        console.log('Initializing Renderer...');
        
        // Set up pixel-perfect rendering
        this.setupPixelPerfectRendering();
        
        console.log('Renderer initialized');
    }
    
    setupPixelPerfectRendering() {
        // Disable image smoothing for pixel art
        this.ctx.imageSmoothingEnabled = false;
        this.ctx.webkitImageSmoothingEnabled = false;
        this.ctx.mozImageSmoothingEnabled = false;
        this.ctx.msImageSmoothingEnabled = false;
    }
    
    // Camera methods
    setCamera(x, y) {
        this.camera.targetX = x;
        this.camera.targetY = y;
    }
    
    updateCamera(deltaTime) {
        // Smooth camera movement
        const smoothing = this.camera.smoothing;
        this.camera.x += (this.camera.targetX - this.camera.x) * smoothing;
        this.camera.y += (this.camera.targetY - this.camera.y) * smoothing;
    }
    
    // Coordinate conversion
    worldToScreen(worldX, worldY) {
        return {
            x: (worldX - this.camera.x) * this.pixelScale,
            y: (worldY - this.camera.y) * this.pixelScale
        };
    }
    
    screenToWorld(screenX, screenY) {
        return {
            x: (screenX / this.pixelScale) + this.camera.x,
            y: (screenY / this.pixelScale) + this.camera.y
        };
    }
    
    // Drawing methods
    clear(color = '#2d5016') {
        this.ctx.fillStyle = color;
        this.ctx.fillRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
    }
    
    drawSprite(sprite, x, y, width = null, height = null) {
        if (!sprite) return;
        
        const screenPos = this.worldToScreen(x, y);
        const drawWidth = (width || sprite.width) * this.pixelScale;
        const drawHeight = (height || sprite.height) * this.pixelScale;
        
        this.ctx.drawImage(
            sprite,
            Math.floor(screenPos.x),
            Math.floor(screenPos.y),
            drawWidth,
            drawHeight
        );
    }
    
    drawTile(tile, x, y) {
        if (!tile) return;
        
        const screenPos = this.worldToScreen(x * this.tileSize, y * this.tileSize);
        const size = this.tileSize * this.pixelScale;
        
        this.ctx.drawImage(
            tile,
            Math.floor(screenPos.x),
            Math.floor(screenPos.y),
            size,
            size
        );
    }
    
    drawCharacter(sprite, x, y, frame = 0) {
        if (!sprite) return;
        
        const screenPos = this.worldToScreen(x, y);
        const size = this.spriteSize * this.pixelScale;
        
        // If sprite is a sprite sheet, draw specific frame
        if (sprite.frames) {
            const frameSprite = sprite.frames[frame % sprite.frames.length];
            this.ctx.drawImage(
                frameSprite,
                Math.floor(screenPos.x),
                Math.floor(screenPos.y),
                size,
                size
            );
        } else {
            this.ctx.drawImage(
                sprite,
                Math.floor(screenPos.x),
                Math.floor(screenPos.y),
                size,
                size
            );
        }
    }
    
    drawText(text, x, y, options = {}) {
        const {
            font = '12px monospace',
            color = 'white',
            align = 'left',
            baseline = 'top',
            outline = false,
            outlineColor = 'black'
        } = options;
        
        const screenPos = this.worldToScreen(x, y);
        
        this.ctx.font = font;
        this.ctx.textAlign = align;
        this.ctx.textBaseline = baseline;
        
        // Draw outline if requested
        if (outline) {
            this.ctx.strokeStyle = outlineColor;
            this.ctx.lineWidth = 2;
            this.ctx.strokeText(text, screenPos.x, screenPos.y);
        }
        
        // Draw main text
        this.ctx.fillStyle = color;
        this.ctx.fillText(text, screenPos.x, screenPos.y);
    }
    
    drawRect(x, y, width, height, color, filled = true) {
        const screenPos = this.worldToScreen(x, y);
        const screenWidth = width * this.pixelScale;
        const screenHeight = height * this.pixelScale;
        
        this.ctx.strokeStyle = color;
        this.ctx.fillStyle = color;
        
        if (filled) {
            this.ctx.fillRect(screenPos.x, screenPos.y, screenWidth, screenHeight);
        } else {
            this.ctx.strokeRect(screenPos.x, screenPos.y, screenWidth, screenHeight);
        }
    }
    
    drawCircle(x, y, radius, color, filled = true) {
        const screenPos = this.worldToScreen(x, y);
        const screenRadius = radius * this.pixelScale;
        
        this.ctx.beginPath();
        this.ctx.arc(screenPos.x, screenPos.y, screenRadius, 0, Math.PI * 2);
        
        this.ctx.strokeStyle = color;
        this.ctx.fillStyle = color;
        
        if (filled) {
            this.ctx.fill();
        } else {
            this.ctx.stroke();
        }
    }
    
    // UI drawing methods (screen space)
    drawUISprite(sprite, x, y, width = null, height = null) {
        if (!sprite) return;
        
        const drawWidth = (width || sprite.width) * this.pixelScale;
        const drawHeight = (height || sprite.height) * this.pixelScale;
        
        this.ctx.drawImage(
            sprite,
            Math.floor(x),
            Math.floor(y),
            drawWidth,
            drawHeight
        );
    }
    
    drawUIText(text, x, y, options = {}) {
        const {
            font = '12px monospace',
            color = 'white',
            align = 'left',
            baseline = 'top'
        } = options;
        
        this.ctx.font = font;
        this.ctx.textAlign = align;
        this.ctx.textBaseline = baseline;
        this.ctx.fillStyle = color;
        this.ctx.fillText(text, x, y);
    }
    
    drawUIRect(x, y, width, height, color, filled = true) {
        this.ctx.strokeStyle = color;
        this.ctx.fillStyle = color;
        
        if (filled) {
            this.ctx.fillRect(x, y, width, height);
        } else {
            this.ctx.strokeRect(x, y, width, height);
        }
    }
    
    // Progress bar
    drawProgressBar(x, y, width, height, progress, bgColor = '#333', fillColor = '#4CAF50') {
        // Background
        this.drawUIRect(x, y, width, height, bgColor, true);
        
        // Fill
        const fillWidth = width * Math.max(0, Math.min(1, progress));
        this.drawUIRect(x, y, fillWidth, height, fillColor, true);
        
        // Border
        this.drawUIRect(x, y, width, height, '#666', false);
    }
    
    // Health bar
    drawHealthBar(x, y, width, height, current, max) {
        const progress = current / max;
        let fillColor = '#4CAF50'; // Green
        
        if (progress < 0.3) {
            fillColor = '#F44336'; // Red
        } else if (progress < 0.6) {
            fillColor = '#FF9800'; // Orange
        }
        
        this.drawProgressBar(x, y, width, height, progress, '#333', fillColor);
    }
    
    // Debug rendering
    drawDebugGrid() {
        if (!this.debugMode) return;
        
        const gridSize = this.tileSize;
        const startX = Math.floor(this.camera.x / gridSize) * gridSize;
        const startY = Math.floor(this.camera.y / gridSize) * gridSize;
        const endX = startX + (this.ctx.canvas.width / this.pixelScale) + gridSize;
        const endY = startY + (this.ctx.canvas.height / this.pixelScale) + gridSize;
        
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
        this.ctx.lineWidth = 1;
        
        // Vertical lines
        for (let x = startX; x <= endX; x += gridSize) {
            const screenX = (x - this.camera.x) * this.pixelScale;
            this.ctx.beginPath();
            this.ctx.moveTo(screenX, 0);
            this.ctx.lineTo(screenX, this.ctx.canvas.height);
            this.ctx.stroke();
        }
        
        // Horizontal lines
        for (let y = startY; y <= endY; y += gridSize) {
            const screenY = (y - this.camera.y) * this.pixelScale;
            this.ctx.beginPath();
            this.ctx.moveTo(0, screenY);
            this.ctx.lineTo(this.ctx.canvas.width, screenY);
            this.ctx.stroke();
        }
    }
    
    drawDebugInfo(x, y, info) {
        if (!this.debugMode) return;
        
        this.drawUIText(info, x, y, {
            font: '10px monospace',
            color: 'yellow'
        });
    }
    
    // Render queue system
    addToRenderQueue(item) {
        this.renderQueue.push(item);
    }
    
    processRenderQueue() {
        // Sort by layer and y-position for proper depth
        this.renderQueue.sort((a, b) => {
            if (a.layer !== b.layer) {
                return a.layer - b.layer;
            }
            return a.y - b.y;
        });
        
        // Render all items
        for (const item of this.renderQueue) {
            item.render(this);
        }
        
        // Clear queue
        this.renderQueue = [];
    }
    
    setDebugMode(enabled) {
        this.debugMode = enabled;
    }
}
