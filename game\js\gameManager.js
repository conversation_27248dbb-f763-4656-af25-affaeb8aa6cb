/**
 * Game Manager - Coordinates all game systems and manages game state
 */

class GameManager {
    constructor(game) {
        this.game = game;
        
        // Game state management
        this.currentChapter = null;
        this.gameProgress = {
            chapter: 'stone_formula',
            completedQuests: [],
            unlockedAreas: ['starting_forest'],
            discoveredRecipes: ['stone_axe', 'wooden_spear'],
            sciencePoints: 0,
            daysPassed: 0
        };
        
        // Game settings
        this.settings = {
            difficulty: 'normal',
            autoSave: true,
            autoSaveInterval: 300000, // 5 minutes
            showTutorials: true
        };
        
        // Event system
        this.events = new Map();
        this.eventQueue = [];
        
        // Auto-save timer
        this.autoSaveTimer = 0;
    }
    
    async init() {
        console.log('Initializing Game Manager...');
        
        // Set up event listeners
        this.setupEventSystem();
        
        // Initialize game progress
        this.initializeGameProgress();
        
        console.log('Game Manager initialized');
    }
    
    setupEventSystem() {
        // Register for important game events
        this.on('questCompleted', (quest) => this.onQuestCompleted(quest));
        this.on('areaUnlocked', (area) => this.onAreaUnlocked(area));
        this.on('recipeDiscovered', (recipe) => this.onRecipeDiscovered(recipe));
        this.on('chapterCompleted', (chapter) => this.onChapterCompleted(chapter));
    }
    
    initializeGameProgress() {
        // Set up initial game state
        this.gameProgress.startTime = Date.now();
        
        // Initialize starting inventory
        const player = this.game.characterEngine.getPlayer();
        if (player) {
            player.inventory.addItem('stone', 5);
            player.inventory.addItem('wood', 3);
        }
    }
    
    update(deltaTime) {
        // Process event queue
        this.processEventQueue();
        
        // Handle auto-save
        if (this.settings.autoSave) {
            this.autoSaveTimer += deltaTime * 1000;
            if (this.autoSaveTimer >= this.settings.autoSaveInterval) {
                this.autoSave();
                this.autoSaveTimer = 0;
            }
        }
        
        // Update game time
        this.updateGameTime(deltaTime);
    }
    
    updateGameTime(deltaTime) {
        // Simple day/night cycle (1 real minute = 1 game hour)
        const gameTimeSpeed = 60; // 60x speed
        this.gameProgress.gameTime = (this.gameProgress.gameTime || 0) + (deltaTime * gameTimeSpeed);
        
        // Check for day progression
        const hoursPerDay = 24;
        const secondsPerDay = hoursPerDay * 60; // 24 minutes real time = 1 game day
        
        if (this.gameProgress.gameTime >= secondsPerDay) {
            this.gameProgress.daysPassed++;
            this.gameProgress.gameTime -= secondsPerDay;
            this.emit('dayPassed', this.gameProgress.daysPassed);
        }
    }
    
    // Event system methods
    on(eventName, callback) {
        if (!this.events.has(eventName)) {
            this.events.set(eventName, []);
        }
        this.events.get(eventName).push(callback);
    }
    
    emit(eventName, data) {
        this.eventQueue.push({ eventName, data, timestamp: Date.now() });
    }
    
    processEventQueue() {
        while (this.eventQueue.length > 0) {
            const event = this.eventQueue.shift();
            const callbacks = this.events.get(event.eventName);
            
            if (callbacks) {
                callbacks.forEach(callback => {
                    try {
                        callback(event.data);
                    } catch (error) {
                        console.error(`Error processing event ${event.eventName}:`, error);
                    }
                });
            }
        }
    }
    
    // Event handlers
    onQuestCompleted(quest) {
        console.log(`Quest completed: ${quest.id}`);
        this.gameProgress.completedQuests.push(quest.id);
        
        // Award science points
        this.gameProgress.sciencePoints += quest.sciencePointReward || 10;
        
        // Check for chapter progression
        this.checkChapterProgression();
    }
    
    onAreaUnlocked(area) {
        console.log(`Area unlocked: ${area}`);
        if (!this.gameProgress.unlockedAreas.includes(area)) {
            this.gameProgress.unlockedAreas.push(area);
        }
    }
    
    onRecipeDiscovered(recipe) {
        console.log(`Recipe discovered: ${recipe}`);
        if (!this.gameProgress.discoveredRecipes.includes(recipe)) {
            this.gameProgress.discoveredRecipes.push(recipe);
        }
    }
    
    onChapterCompleted(chapter) {
        console.log(`Chapter completed: ${chapter}`);
        
        // Determine next chapter
        const chapterProgression = {
            'stone_formula': 'kingdom_of_science',
            'kingdom_of_science': 'stone_wars',
            'stone_wars': 'age_of_exploration'
        };
        
        const nextChapter = chapterProgression[chapter];
        if (nextChapter) {
            this.startChapter(nextChapter);
        }
    }
    
    checkChapterProgression() {
        // Check if current chapter objectives are met
        const currentChapter = this.currentChapter;
        if (!currentChapter) return;
        
        // Chapter-specific progression logic
        switch (currentChapter) {
            case 'stone_formula':
                if (this.gameProgress.completedQuests.includes('create_revival_fluid')) {
                    this.emit('chapterCompleted', 'stone_formula');
                }
                break;
                
            case 'kingdom_of_science':
                if (this.gameProgress.completedQuests.includes('unite_village')) {
                    this.emit('chapterCompleted', 'kingdom_of_science');
                }
                break;
        }
    }
    
    startChapter(chapterId) {
        console.log(`Starting chapter: ${chapterId}`);
        this.currentChapter = chapterId;
        this.gameProgress.chapter = chapterId;
        
        // Notify story engine
        this.game.storyEngine.startChapter(chapterId);
    }
    
    // Save/Load system
    createSaveData() {
        return {
            version: '1.0.0',
            timestamp: Date.now(),
            gameProgress: { ...this.gameProgress },
            settings: { ...this.settings },
            characterData: this.game.characterEngine.getSaveData(),
            worldData: this.game.worldEngine.getSaveData(),
            storyData: this.game.storyEngine.getSaveData(),
            craftingData: this.game.craftingEngine.getSaveData()
        };
    }
    
    loadSaveData(saveData) {
        try {
            // Validate save data
            if (!saveData.version || !saveData.gameProgress) {
                throw new Error('Invalid save data');
            }
            
            // Load game progress
            this.gameProgress = { ...saveData.gameProgress };
            this.settings = { ...saveData.settings };
            
            // Load system data
            this.game.characterEngine.loadSaveData(saveData.characterData);
            this.game.worldEngine.loadSaveData(saveData.worldData);
            this.game.storyEngine.loadSaveData(saveData.storyData);
            this.game.craftingEngine.loadSaveData(saveData.craftingData);
            
            // Resume current chapter
            this.currentChapter = this.gameProgress.chapter;
            
            console.log('Save data loaded successfully');
            return true;
        } catch (error) {
            console.error('Failed to load save data:', error);
            return false;
        }
    }
    
    autoSave() {
        try {
            const saveData = this.createSaveData();
            localStorage.setItem('drstone_autosave', JSON.stringify(saveData));
            console.log('Auto-save completed');
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    }
    
    // Utility methods
    isQuestCompleted(questId) {
        return this.gameProgress.completedQuests.includes(questId);
    }
    
    isAreaUnlocked(areaId) {
        return this.gameProgress.unlockedAreas.includes(areaId);
    }
    
    isRecipeDiscovered(recipeId) {
        return this.gameProgress.discoveredRecipes.includes(recipeId);
    }
    
    getSciencePoints() {
        return this.gameProgress.sciencePoints;
    }
    
    spendSciencePoints(amount) {
        if (this.gameProgress.sciencePoints >= amount) {
            this.gameProgress.sciencePoints -= amount;
            return true;
        }
        return false;
    }
    
    getCurrentChapter() {
        return this.currentChapter;
    }
    
    getGameProgress() {
        return { ...this.gameProgress };
    }
}
