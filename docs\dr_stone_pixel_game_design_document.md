# Dr. Stone: Pixel Kingdom - Game Design Document

## 1. Introduction

This document provides a comprehensive design for the 2D pixel art RPG, **Dr. Stone: Pixel Kingdom**. It expands upon the initial game concept, providing detailed technical specifications, system architecture diagrams, UI mockups, and a roadmap for implementation. This document is intended to be a living guide for the development team.

## 2. Game Flow

The game will follow a chapter-based structure, mirroring the story arcs of the *Dr<PERSON> Stone* manga and anime. The core gameplay loop can be summarized as follows:

```
+-----------------------+
|      Story Event      |
+-----------------------+
          |
V
+-----------------------+
|   New Area Unlocked   |
+-----------------------+
          |
V
+-----------------------+
| Gather & Explore Loop |
|  - Collect Resources  |
|  - Discover Schematics|
|  - Battle Enemies     |
+-----------------------+
          |
V
+-----------------------+
|  Crafting & Research  |
|  - Unlock <PERSON> Tree   |
|  - Craft New Items    |
+-----------------------+
          |
V
+-----------------------+
|   Story Progression   |
| (New Abilities/Areas) |
+-----------------------+
```

## 3. System Architecture

The game will be built on a modular architecture, allowing for easier development and expansion. The core systems are:

*   **Story Engine:** Manages game events, quests, and dialogue.
*   **Crafting Engine:** Handles all crafting-related mechanics, including the tech tree, recipes, and mini-games.
*   **Character Engine:** Manages player and NPC stats, skills, and progression.
*   **World Engine:** Controls the game world, including level loading, resource spawning, and enemy encounters.
*   **Combat Engine:** Manages the turn-based combat system.

```
+--------------------+
|    Game Manager    |
+--------------------+
|                    |
|  +---------------+  |
|  |  Story Engine |  |
|  +---------------+  |
|                    |
|  +---------------+  |
|  | Crafting Eng. |  |
|  +---------------+  |
|                    |
|  +---------------+  |
|  | Character Eng.|  |
|  +---------------+  |
|                    |
|  +---------------+  |
|  |  World Engine |  |
|  +---------------+  |
|                    |
|  +---------------+  |
|  |  Combat Eng.  |  |
|  +---------------+  |
|                    |
+--------------------+
```

## 4. UI/UX Design

The UI will be designed to be clean, intuitive, and consistent with the pixel art style of the game. The main HUD will display essential information without cluttering the screen.

**HUD Mockup:**

```
+----------------------------------------------------------------------+
| [Character Portrait] [HP: 100/100] [SP: 50/50] | [Stamina: 100%]       |
|                                              | [Hunger: 80%]         |
|                                              | [Thirst: 75%]         |
+----------------------------------------------------------------------+
|                                                                      |
|                                                                      |
|                                                                      |
|                            GAMEPLAY AREA                             |
|                                                                      |
|                                                                      |
|                                                                      |
+----------------------------------------------------------------------+
| [Quick Slot 1] [Quick Slot 2] [Quick Slot 3] [Quick Slot 4] | [MENU] |
+----------------------------------------------------------------------+
```

**Main Menu Mockup:**

```
+-----------------------+
|       MAIN MENU       |
+-----------------------+
|       [ Inventory ]       |
|       [ Crafting  ]       |
|       [ Tech Tree ]       |
|       [   Party   ]       |
|       [   Quests  ]       |
|       [   Save    ]       |
|       [   Load    ]       |
|       [  Options  ]       |
+-----------------------+
```

## 5. Crafting System

The crafting system is central to the game and will be represented by a detailed tech tree. The workflow for crafting is as follows:

1.  **Discover a Need:** A story event or environmental obstacle will present a problem that needs a scientific solution.
2.  **Formulate a Plan:** Senku will brainstorm a solution, creating a new schematic.
3.  **Gather Resources:** The player must gather the necessary resources from the environment.
4.  **Craft the Item:** The player will use a crafting station (workbench, furnace, etc.) to create the item. This may involve a mini-game for more complex items.

**Tech Tree Visualization (Simplified):**

```
(Stone Age) -> [Stone Tools] -> [Basic Shelter]
      |
      V
(Bronze Age) -> [Bronze Tools] -> [Furnace]
      |
      V
(Iron Age) -> [Iron Tools] -> [Basic Machinery]
      |
      V
(Steam Age) -> [Steam Engine] -> [Automobile]
```

## 6. Character Progression

Character progression is tied to both individual character growth and the overall advancement of the Kingdom of Science. Characters will level up through a combination of combat, crafting, and completing quests.

**Character Stats:**

*   **HP (Health Points):** Determines how much damage a character can take before being knocked out.
*   **SP (Stamina Points):** Used for special abilities and powerful attacks.
*   **Strength (STR):** Affects physical attack power and carrying capacity.
*   **Defense (DEF):** Reduces damage from physical attacks.
*   **Intelligence (INT):** Affects the power of scientific inventions and the effectiveness of certain skills.
*   **Speed (SPD):** Determines turn order in combat and affects evasion.
*   **Dexterity (DEX):** Affects accuracy and the success rate of crafting mini-games.

**Character Roles and Growth:**

| Character      | Role         | Primary Stats | Growth Focus                                  |
|----------------|--------------|---------------|-----------------------------------------------|
| **Senku**      | Scientist    | INT, DEX      | Unlocking new tech, crafting high-quality items |
| **Taiju**      | Gatherer/Tank | STR, DEF      | Resource gathering, defense in combat         |
| **Yuzuriha**   | Crafter      | DEX, INT      | Crafting clothing, support items              |
| **Kohaku**     | Warrior      | SPD, STR      | Fast attacks, scouting, and exploration       |
| **Chrome**     | Sorcerer/Sci | INT, SPD      | Versatile combat and crafting                 |
| **Suika**      | Scout        | SPD, DEX      | Stealth, exploration, finding hidden items    |

## 7. Level Design

The world of **Dr. Stone: Pixel Kingdom** will be a large, seamless map, with different regions unlocking as the story progresses. Each region will have a distinct visual style, set of resources, and enemies.

**World Map Structure:**

```
                               +------------------+
                               |  Treasure Island |
                               +------------------+
                                     ^
                                     |
+-----------------+         +------------------+         +-----------------+
|  Empire of Might| <-----> |   Ishigami Village | <-----> |    Cave of      |
| (Late Game)     |         | (Central Hub)    |         |    Miracles     |
+-----------------+         +------------------+         +-----------------+
                                     ^
                                     |
                               +------------------+
                               |  Starting Forest |
                               +------------------+
```

**Level Design Philosophy:**

*   **Reward Exploration:** Hidden areas, rare resources, and secret schematics will be scattered throughout the world to encourage exploration.
*   **Environmental Puzzles:** Some areas will require the use of specific crafted items or character abilities to progress.
*   **Dynamic Environments:** Certain areas will change based on the story, such as the construction of the Kingdom of Science or the aftermath of a major battle.

## 8. Implementation Roadmap

The development of **Dr. Stone: Pixel Kingdom** will be divided into four phases:

**Phase 1: Prototyping (3 Months)**

*   **Goal:** Develop a playable prototype with the core gameplay mechanics.
*   **Focus:** Crafting system, combat system, and character movement.
*   **Deliverable:** A vertical slice of the game, showcasing the main gameplay loop.

**Phase 2: Pre-Production (6 Months)**

*   **Goal:** Finalize the game design and create all necessary assets.
*   **Focus:** Story writing, character design, level design, and UI/UX design.
*   **Deliverable:** A complete Game Design Document and all art and audio assets.

**Phase 3: Production (12 Months)**

*   **Goal:** Develop the full game.
*   **Focus:** Implementing all content, including the story, quests, and all gameplay features.
*   **Deliverable:** A feature-complete beta version of the game.

**Phase 4: Post-Production (3 Months)**

*   **Goal:** Polish the game and prepare it for launch.
*   **Focus:** Bug fixing, playtesting, and marketing.
*   **Deliverable:** The final, polished version of the game.

## 9. Visual Style Guide

The visual style of **Dr. Stone: Pixel Kingdom** will be a key part of its identity. The following guidelines will ensure a consistent and high-quality aesthetic.

**Pixel Art Specifications:**

*   **Resolution:** 480x270 (16:9 aspect ratio)
*   **Palette:** A carefully selected palette of 32 colors will be used for all in-game assets. This will create a cohesive and visually appealing look.
*   **Character Sprites:** 32x32 pixels. Animations should be a minimum of 4 frames for walking and running.
*   **Tileset:** 16x16 pixel tiles will be used for all environments.
*   **UI Elements:** All UI elements will be created in a pixel art style, with a focus on readability and ease of use.

**Visual References:**

The following games will be used as visual references for the art style:

*   **Stardew Valley:** For its charming and detailed environments.
*   **Celeste:** For its fluid and expressive character animations.
*   **Hyper Light Drifter:** For its use of a limited color palette to create a strong sense of atmosphere.
