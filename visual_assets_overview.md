# Dr. Stone Pixel Game - Visual Assets Overview

## Character Sprites (32x32 pixels)

### Main Characters
- **<PERSON><PERSON>**: `/workspace/imgs/characters/senku_pixel_sprite.png`
  - Green hair, lab coat, holding beaker
  - Primary protagonist sprite with walking animation frames

- **Kohaku**: `/workspace/imgs/characters/kohaku_pixel_sprite.png`
  - <PERSON><PERSON><PERSON> hair, warrior outfit, holding spear
  - Combat-ready stance, fierce expression

- **Chrome**: `/workspace/imgs/characters/chrome_pixel_sprite.png`
  - Brown hair, apprentice outfit, holding crystal
  - Curious expression, science apprentice design

## UI/UX Mockups

### Game Interface Elements
- **Main Game UI**: `/workspace/imgs/ui_mockups/game_ui_mockup.png`
  - Inventory screen with crafting grid
  - Science points counter, health bar
  - Blue and white color scheme

- **Crafting Interface**: `/workspace/imgs/ui_mockups/crafting_interface.png`
  - Recipe grid layout with ingredient slots
  - Progress bar and action buttons
  - Clean 16-bit retro design

- **Title Screen**: `/workspace/imgs/ui_mockups/title_screen_concept.png`
  - Logo design concept
  - Stone age meets science theme
  - Retro typography styling

## Concept Art & Environments

### World Design
- **World Map Overview**: `/workspace/imgs/concept_art/world_map_concept.png`
  - Post-apocalyptic world with stone statues
  - Lush forests, mountains, rivers, settlements
  - Overhead 16-bit style view

- **Laboratory Interior**: `/workspace/imgs/concept_art/laboratory_interior.png`
  - Crafting stations, furnaces, workbenches
  - Science equipment and test tubes
  - Background tileset design

- **Village Environment**: `/workspace/imgs/concept_art/village_environment.png`
  - Stone age buildings with thatched roofs
  - Fire pits, stone statues, vegetation
  - Environmental background assets

### System Diagrams
- **Tech Tree Visualization**: `/workspace/imgs/concept_art/tech_tree_diagram.png`
  - Connected progression nodes
  - Stone tools to advanced technology
  - Scientific formulas and symbols

## Visual Style Guide

### Pixel Art Specifications
- **Resolution**: 32x32 pixels for character sprites
- **Color Palette**: 16-bit retro style with emphasis on earth tones and science colors
- **Animation**: 4-frame walking cycles for character movement
- **UI Elements**: Clean, readable pixel fonts with consistent spacing

### Character Design Principles
- Authentic to Dr. Stone character designs
- Readable at small pixel resolutions
- Distinctive silhouettes for easy recognition
- Consistent art style across all characters

### Environment Design
- Post-apocalyptic nature reclaimed world
- Scientific equipment integrated with primitive tools
- Lush vegetation contrasting with stone structures
- Clear visual hierarchy for gameplay elements

## Implementation Notes

### Asset Organization
- Character sprites: 32x32 pixel sheets with animation frames
- UI elements: Scalable components for different screen sizes
- Background tiles: Modular 16x16 pixel tiles for level construction
- Effects: Particle systems for crafting and science experiments

### Technical Specifications
- PNG format with transparency for all sprites
- Consistent pixel density across all assets
- Organized sprite sheets for efficient memory usage
- Clear naming conventions for easy asset management

This visual package provides a comprehensive foundation for the Dr. Stone pixel game development, maintaining authentic character designs while optimizing for pixel art aesthetics and gameplay functionality.
