<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dr. Stone: Pixel Kingdom</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Courier New', monospace;
        }
        
        #gameContainer {
            position: relative;
            border: 2px solid #4a4a4a;
            background: #000;
        }
        
        #gameCanvas {
            display: block;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
        }
        
        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            color: white;
            font-size: 12px;
        }
        
        .ui-element {
            position: absolute;
            pointer-events: auto;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #4a4a4a;
            padding: 5px;
        }
        
        #hud {
            top: 10px;
            left: 10px;
            right: 10px;
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        #quickSlots {
            bottom: 10px;
            left: 10px;
            right: 10px;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }
        
        .quick-slot {
            width: 40px;
            height: 40px;
            border: 2px solid #666;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .quick-slot:hover {
            border-color: #aaa;
        }
        
        #menu {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #666;
            padding: 20px;
            min-width: 300px;
        }
        
        .menu-button {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: #333;
            color: white;
            border: 1px solid #666;
            cursor: pointer;
            text-align: center;
        }
        
        .menu-button:hover {
            background: #555;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas" width="960" height="540"></canvas>
        
        <div id="ui">
            <div id="hud" class="ui-element">
                <div id="characterInfo">
                    <div>Senku Ishigami</div>
                    <div>HP: <span id="hp">100</span>/100</div>
                    <div>SP: <span id="sp">50</span>/50</div>
                </div>
                <div id="statusBars">
                    <div>Stamina: <span id="stamina">100</span>%</div>
                    <div>Hunger: <span id="hunger">80</span>%</div>
                    <div>Thirst: <span id="thirst">75</span>%</div>
                </div>
            </div>
            
            <div id="quickSlots" class="ui-element">
                <div class="quick-slot" data-slot="0"></div>
                <div class="quick-slot" data-slot="1"></div>
                <div class="quick-slot" data-slot="2"></div>
                <div class="quick-slot" data-slot="3"></div>
                <button id="menuButton" class="menu-button" style="width: auto; margin: 0 0 0 20px;">MENU</button>
            </div>
            
            <div id="menu">
                <h3>Dr. Stone: Pixel Kingdom</h3>
                <button class="menu-button" onclick="game.showInventory()">Inventory</button>
                <button class="menu-button" onclick="game.showCrafting()">Crafting</button>
                <button class="menu-button" onclick="game.showTechTree()">Tech Tree</button>
                <button class="menu-button" onclick="game.showParty()">Party</button>
                <button class="menu-button" onclick="game.showQuests()">Quests</button>
                <button class="menu-button" onclick="game.saveGame()">Save</button>
                <button class="menu-button" onclick="game.loadGame()">Load</button>
                <button class="menu-button" onclick="game.hideMenu()">Close</button>
            </div>
            
            <div id="loading">Loading Dr. Stone: Pixel Kingdom...</div>
        </div>
    </div>
    
    <script src="js/game.js"></script>
    <script src="js/gameManager.js"></script>
    <script src="js/storyEngine.js"></script>
    <script src="js/craftingEngine.js"></script>
    <script src="js/characterEngine.js"></script>
    <script src="js/worldEngine.js"></script>
    <script src="js/combatEngine.js"></script>
    <script src="js/renderer.js"></script>
    <script src="js/input.js"></script>
    <script src="js/assets.js"></script>
    
    <script>
        // Initialize the game when page loads
        window.addEventListener('load', () => {
            const game = new Game();
            window.game = game; // Make game globally accessible for debugging
            game.init();
        });
        
        // Menu toggle functionality
        document.getElementById('menuButton').addEventListener('click', () => {
            const menu = document.getElementById('menu');
            menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
        });
    </script>
</body>
</html>
