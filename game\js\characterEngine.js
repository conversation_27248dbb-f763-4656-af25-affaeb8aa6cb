/**
 * Character Engine - Manages player and NPC characters, stats, and progression
 */

class CharacterEngine {
    constructor(game) {
        this.game = game;
        
        // Character management
        this.player = null;
        this.party = [];
        this.npcs = new Map();
        
        // Character definitions
        this.characterDefinitions = {
            senku: {
                name: '<PERSON><PERSON>',
                sprite: 'senku',
                role: 'Scientist',
                baseStats: {
                    hp: 80, maxHp: 80,
                    sp: 100, maxSp: 100,
                    str: 60, def: 50,
                    int: 100, spd: 70, dex: 85,
                    stamina: 100, hunger: 100, thirst: 100
                },
                skills: ['crafting_master', 'science_genius', 'formula_creation'],
                description: 'A brilliant scientist with 10 billion percent confidence!'
            },
            kohaku: {
                name: '<PERSON><PERSON><PERSON>',
                sprite: 'kohaku',
                role: 'Warrior',
                baseStats: {
                    hp: 120, maxHp: 120,
                    sp: 80, maxSp: 80,
                    str: 95, def: 80,
                    int: 70, spd: 90, dex: 75,
                    stamina: 100, hunger: 100, thirst: 100
                },
                skills: ['combat_expert', 'lioness_strength', 'protective_instinct'],
                description: 'A fierce warrior with the strength of a lioness.'
            },
            chrome: {
                name: 'Chrome',
                sprite: 'chrome',
                role: 'Sorcerer/Scientist',
                baseStats: {
                    hp: 90, maxHp: 90,
                    sp: 90, maxSp: 90,
                    str: 70, def: 60,
                    int: 85, spd: 75, dex: 80,
                    stamina: 100, hunger: 100, thirst: 100
                },
                skills: ['mineral_knowledge', 'sorcery_basics', 'curious_mind'],
                description: 'A curious sorcerer eager to learn about science.'
            }
        };
    }
    
    async init() {
        console.log('Initializing Character Engine...');
        
        // Create the player character (Senku)
        this.createPlayer('senku');
        
        // Initialize inventory system
        this.initializeInventory();
        
        console.log('Character Engine initialized');
    }
    
    createPlayer(characterId) {
        const definition = this.characterDefinitions[characterId];
        if (!definition) {
            throw new Error(`Character definition not found: ${characterId}`);
        }
        
        this.player = new Character(characterId, definition, true);
        this.player.x = 480; // Center of screen
        this.player.y = 270;
        
        console.log(`Player created: ${this.player.name}`);
        return this.player;
    }
    
    createNPC(characterId, x, y) {
        const definition = this.characterDefinitions[characterId];
        if (!definition) {
            throw new Error(`Character definition not found: ${characterId}`);
        }
        
        const npc = new Character(characterId, definition, false);
        npc.x = x;
        npc.y = y;
        
        this.npcs.set(npc.id, npc);
        console.log(`NPC created: ${npc.name} at (${x}, ${y})`);
        return npc;
    }
    
    addToParty(characterId) {
        const character = this.npcs.get(characterId);
        if (character && !this.party.includes(character)) {
            this.party.push(character);
            console.log(`${character.name} joined the party!`);
            return true;
        }
        return false;
    }
    
    removeFromParty(characterId) {
        const index = this.party.findIndex(char => char.id === characterId);
        if (index !== -1) {
            const character = this.party.splice(index, 1)[0];
            console.log(`${character.name} left the party.`);
            return true;
        }
        return false;
    }
    
    initializeInventory() {
        if (this.player) {
            this.player.inventory = new Inventory(40); // 40 slots
            
            // Add starting items
            this.player.inventory.addItem('stone', 5);
            this.player.inventory.addItem('wood', 3);
        }
    }
    
    update(deltaTime) {
        // Update player
        if (this.player) {
            this.player.update(deltaTime);
        }
        
        // Update party members
        for (const character of this.party) {
            character.update(deltaTime);
        }
        
        // Update NPCs
        for (const npc of this.npcs.values()) {
            npc.update(deltaTime);
        }
        
        // Update camera to follow player
        if (this.player) {
            this.game.renderer.setCamera(
                this.player.x - this.game.settings.resolution.width / 4,
                this.player.y - this.game.settings.resolution.height / 4
            );
        }
    }
    
    render(renderer) {
        // Render NPCs
        for (const npc of this.npcs.values()) {
            npc.render(renderer);
        }
        
        // Render party members
        for (const character of this.party) {
            character.render(renderer);
        }
        
        // Render player last (on top)
        if (this.player) {
            this.player.render(renderer);
        }
    }
    
    getPlayer() {
        return this.player;
    }
    
    getParty() {
        return [...this.party];
    }
    
    getNPC(id) {
        return this.npcs.get(id);
    }
    
    getAllCharacters() {
        const characters = [this.player, ...this.party];
        for (const npc of this.npcs.values()) {
            characters.push(npc);
        }
        return characters.filter(char => char !== null);
    }
    
    // UI Methods
    showInventory() {
        console.log('Showing inventory...');
        // TODO: Implement inventory UI
    }
    
    showPartyManagement() {
        console.log('Showing party management...');
        // TODO: Implement party management UI
    }
    
    // Save/Load
    getSaveData() {
        return {
            player: this.player ? this.player.getSaveData() : null,
            party: this.party.map(char => char.getSaveData()),
            npcs: Array.from(this.npcs.entries()).map(([id, npc]) => ({
                id,
                data: npc.getSaveData()
            }))
        };
    }
    
    loadSaveData(data) {
        if (data.player) {
            this.player = Character.fromSaveData(data.player);
        }
        
        this.party = data.party.map(charData => Character.fromSaveData(charData));
        
        this.npcs.clear();
        for (const npcEntry of data.npcs) {
            const npc = Character.fromSaveData(npcEntry.data);
            this.npcs.set(npcEntry.id, npc);
        }
    }
}

/**
 * Character class representing individual characters
 */
class Character {
    constructor(id, definition, isPlayer = false) {
        this.id = id;
        this.name = definition.name;
        this.sprite = definition.sprite;
        this.role = definition.role;
        this.isPlayer = isPlayer;
        
        // Position
        this.x = 0;
        this.y = 0;
        this.direction = 'down';
        
        // Stats
        this.stats = { ...definition.baseStats };
        this.level = 1;
        this.experience = 0;
        this.experienceToNext = 100;
        
        // Skills
        this.skills = [...definition.skills];
        this.skillLevels = new Map();
        
        // Inventory (only for player and party members)
        this.inventory = null;
        
        // Animation
        this.animationFrame = 0;
        this.animationTimer = 0;
        this.animationSpeed = 0.2;
        this.isMoving = false;
        
        // Movement
        this.moveSpeed = 100; // pixels per second
        this.targetX = this.x;
        this.targetY = this.y;
        
        // Initialize skills
        for (const skill of this.skills) {
            this.skillLevels.set(skill, 1);
        }
    }
    
    move(dx, dy) {
        if (!this.isPlayer) return;
        
        const newX = this.x + dx * this.moveSpeed * (1/60); // Assuming 60 FPS
        const newY = this.y + dy * this.moveSpeed * (1/60);
        
        // Simple bounds checking
        if (newX >= 0 && newX <= 2000 && newY >= 0 && newY <= 2000) {
            this.x = newX;
            this.y = newY;
            this.isMoving = dx !== 0 || dy !== 0;
            
            // Update direction
            if (dx > 0) this.direction = 'right';
            else if (dx < 0) this.direction = 'left';
            else if (dy > 0) this.direction = 'down';
            else if (dy < 0) this.direction = 'up';
        }
    }
    
    update(deltaTime) {
        // Update animation
        if (this.isMoving) {
            this.animationTimer += deltaTime;
            if (this.animationTimer >= this.animationSpeed) {
                this.animationFrame = (this.animationFrame + 1) % 4;
                this.animationTimer = 0;
            }
        } else {
            this.animationFrame = 0;
        }
        
        // Reset movement flag
        this.isMoving = false;
        
        // Update stats (hunger, thirst, etc.)
        this.updateStats(deltaTime);
    }
    
    updateStats(deltaTime) {
        // Gradual hunger and thirst decrease
        this.stats.hunger = Math.max(0, this.stats.hunger - deltaTime * 0.5);
        this.stats.thirst = Math.max(0, this.stats.thirst - deltaTime * 0.8);
        
        // Stamina regeneration
        if (this.stats.stamina < 100) {
            this.stats.stamina = Math.min(100, this.stats.stamina + deltaTime * 10);
        }
        
        // SP regeneration
        if (this.stats.sp < this.stats.maxSp) {
            this.stats.sp = Math.min(this.stats.maxSp, this.stats.sp + deltaTime * 5);
        }
    }
    
    render(renderer) {
        const sprite = renderer.game.assets.getCharacter(this.sprite);
        if (sprite) {
            renderer.drawCharacter(sprite, this.x, this.y, this.animationFrame);
        }
        
        // Draw name above character
        renderer.drawText(this.name, this.x, this.y - 40, {
            font: '10px monospace',
            color: 'white',
            align: 'center',
            outline: true
        });
        
        // Draw health bar for non-player characters
        if (!this.isPlayer && this.stats.hp < this.stats.maxHp) {
            const barWidth = 32;
            const barHeight = 4;
            renderer.drawRect(this.x - barWidth/2, this.y - 50, barWidth, barHeight, '#333', true);
            renderer.drawRect(
                this.x - barWidth/2, this.y - 50,
                barWidth * (this.stats.hp / this.stats.maxHp), barHeight,
                '#4CAF50', true
            );
        }
    }
    
    useQuickSlot(slotIndex) {
        if (this.inventory) {
            const item = this.inventory.getQuickSlot(slotIndex);
            if (item) {
                this.useItem(item);
            }
        }
    }
    
    useItem(item) {
        console.log(`${this.name} used ${item.name}`);
        // TODO: Implement item usage
    }
    
    gainExperience(amount) {
        this.experience += amount;
        
        while (this.experience >= this.experienceToNext) {
            this.levelUp();
        }
    }
    
    levelUp() {
        this.level++;
        this.experience -= this.experienceToNext;
        this.experienceToNext = Math.floor(this.experienceToNext * 1.2);
        
        // Increase stats
        this.stats.maxHp += 5;
        this.stats.maxSp += 3;
        this.stats.hp = this.stats.maxHp;
        this.stats.sp = this.stats.maxSp;
        
        console.log(`${this.name} reached level ${this.level}!`);
    }
    
    getSaveData() {
        return {
            id: this.id,
            name: this.name,
            sprite: this.sprite,
            role: this.role,
            isPlayer: this.isPlayer,
            x: this.x,
            y: this.y,
            direction: this.direction,
            stats: { ...this.stats },
            level: this.level,
            experience: this.experience,
            experienceToNext: this.experienceToNext,
            skills: [...this.skills],
            skillLevels: Array.from(this.skillLevels.entries()),
            inventory: this.inventory ? this.inventory.getSaveData() : null
        };
    }
    
    static fromSaveData(data) {
        const character = new Character(data.id, {
            name: data.name,
            sprite: data.sprite,
            role: data.role,
            baseStats: data.stats,
            skills: data.skills
        }, data.isPlayer);
        
        character.x = data.x;
        character.y = data.y;
        character.direction = data.direction;
        character.level = data.level;
        character.experience = data.experience;
        character.experienceToNext = data.experienceToNext;
        character.skillLevels = new Map(data.skillLevels);
        
        if (data.inventory) {
            character.inventory = Inventory.fromSaveData(data.inventory);
        }
        
        return character;
    }
}

/**
 * Simple Inventory class
 */
class Inventory {
    constructor(size) {
        this.size = size;
        this.items = new Array(size).fill(null);
        this.quickSlots = new Array(4).fill(null);
    }
    
    addItem(itemId, quantity = 1) {
        // Find existing stack or empty slot
        for (let i = 0; i < this.size; i++) {
            if (!this.items[i]) {
                this.items[i] = { id: itemId, quantity };
                return true;
            } else if (this.items[i].id === itemId) {
                this.items[i].quantity += quantity;
                return true;
            }
        }
        return false; // Inventory full
    }
    
    removeItem(itemId, quantity = 1) {
        for (let i = 0; i < this.size; i++) {
            if (this.items[i] && this.items[i].id === itemId) {
                if (this.items[i].quantity <= quantity) {
                    this.items[i] = null;
                } else {
                    this.items[i].quantity -= quantity;
                }
                return true;
            }
        }
        return false;
    }

    hasItem(itemId, quantity = 1) {
        let totalQuantity = 0;
        for (let i = 0; i < this.size; i++) {
            if (this.items[i] && this.items[i].id === itemId) {
                totalQuantity += this.items[i].quantity;
                if (totalQuantity >= quantity) {
                    return true;
                }
            }
        }
        return false;
    }
    
    getQuickSlot(index) {
        return this.quickSlots[index];
    }
    
    setQuickSlot(index, item) {
        this.quickSlots[index] = item;
    }
    
    getSaveData() {
        return {
            size: this.size,
            items: this.items,
            quickSlots: this.quickSlots
        };
    }
    
    static fromSaveData(data) {
        const inventory = new Inventory(data.size);
        inventory.items = data.items;
        inventory.quickSlots = data.quickSlots;
        return inventory;
    }
}
