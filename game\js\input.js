/**
 * Input Manager - <PERSON>les keyboard and mouse input
 */

class InputManager {
    constructor(game) {
        this.game = game;
        
        // Input state
        this.keys = new Map();
        this.mouse = {
            x: 0,
            y: 0,
            buttons: new Map(),
            wheel: 0
        };
        
        // Input bindings
        this.keyBindings = {
            // Movement
            'ArrowUp': 'move_up',
            'ArrowDown': 'move_down',
            'ArrowLeft': 'move_left',
            'ArrowRight': 'move_right',
            'KeyW': 'move_up',
            'KeyS': 'move_down',
            'KeyA': 'move_left',
            'KeyD': 'move_right',
            
            // Actions
            'Space': 'interact',
            'KeyE': 'interact',
            'KeyI': 'inventory',
            'KeyC': 'crafting',
            'KeyT': 'tech_tree',
            'KeyM': 'menu',
            'Escape': 'menu',
            
            // Quick slots
            'Digit1': 'quick_slot_1',
            'Digit2': 'quick_slot_2',
            'Digit3': 'quick_slot_3',
            'Digit4': 'quick_slot_4',
            
            // Debug
            'F1': 'debug_toggle',
            'F2': 'debug_info'
        };
        
        // Action handlers
        this.actionHandlers = new Map();
        this.setupActionHandlers();
        
        // Input events
        this.inputEvents = [];
    }
    
    init() {
        console.log('Initializing Input Manager...');
        
        // Set up event listeners
        this.setupEventListeners();
        
        console.log('Input Manager initialized');
    }
    
    setupEventListeners() {
        const canvas = this.game.canvas;
        
        // Keyboard events
        document.addEventListener('keydown', (e) => this.onKeyDown(e));
        document.addEventListener('keyup', (e) => this.onKeyUp(e));
        
        // Mouse events
        canvas.addEventListener('mousedown', (e) => this.onMouseDown(e));
        canvas.addEventListener('mouseup', (e) => this.onMouseUp(e));
        canvas.addEventListener('mousemove', (e) => this.onMouseMove(e));
        canvas.addEventListener('wheel', (e) => this.onMouseWheel(e));
        
        // Touch events for mobile support
        canvas.addEventListener('touchstart', (e) => this.onTouchStart(e));
        canvas.addEventListener('touchend', (e) => this.onTouchEnd(e));
        canvas.addEventListener('touchmove', (e) => this.onTouchMove(e));
        
        // Prevent context menu on right click
        canvas.addEventListener('contextmenu', (e) => e.preventDefault());
        
        // Focus management
        canvas.tabIndex = 1;
        canvas.focus();
    }
    
    setupActionHandlers() {
        // Movement actions
        this.actionHandlers.set('move_up', () => this.handleMovement(0, -1));
        this.actionHandlers.set('move_down', () => this.handleMovement(0, 1));
        this.actionHandlers.set('move_left', () => this.handleMovement(-1, 0));
        this.actionHandlers.set('move_right', () => this.handleMovement(1, 0));
        
        // UI actions
        this.actionHandlers.set('interact', () => this.handleInteract());
        this.actionHandlers.set('inventory', () => this.game.showInventory());
        this.actionHandlers.set('crafting', () => this.game.showCrafting());
        this.actionHandlers.set('tech_tree', () => this.game.showTechTree());
        this.actionHandlers.set('menu', () => this.toggleMenu());
        
        // Quick slot actions
        this.actionHandlers.set('quick_slot_1', () => this.useQuickSlot(0));
        this.actionHandlers.set('quick_slot_2', () => this.useQuickSlot(1));
        this.actionHandlers.set('quick_slot_3', () => this.useQuickSlot(2));
        this.actionHandlers.set('quick_slot_4', () => this.useQuickSlot(3));
        
        // Debug actions
        this.actionHandlers.set('debug_toggle', () => this.toggleDebug());
    }
    
    // Event handlers
    onKeyDown(event) {
        const keyCode = event.code;
        
        // Prevent default for game keys
        if (this.keyBindings[keyCode]) {
            event.preventDefault();
        }
        
        // Update key state
        this.keys.set(keyCode, {
            pressed: true,
            justPressed: !this.keys.has(keyCode) || !this.keys.get(keyCode).pressed,
            timestamp: Date.now()
        });
        
        // Handle action
        const action = this.keyBindings[keyCode];
        if (action && this.keys.get(keyCode).justPressed) {
            this.handleAction(action);
        }
    }
    
    onKeyUp(event) {
        const keyCode = event.code;
        
        // Update key state
        if (this.keys.has(keyCode)) {
            this.keys.set(keyCode, {
                pressed: false,
                justPressed: false,
                timestamp: Date.now()
            });
        }
    }
    
    onMouseDown(event) {
        const rect = this.game.canvas.getBoundingClientRect();
        this.mouse.x = event.clientX - rect.left;
        this.mouse.y = event.clientY - rect.top;
        
        this.mouse.buttons.set(event.button, {
            pressed: true,
            justPressed: true,
            timestamp: Date.now()
        });
        
        // Handle mouse click
        this.handleMouseClick(event.button, this.mouse.x, this.mouse.y);
    }
    
    onMouseUp(event) {
        this.mouse.buttons.set(event.button, {
            pressed: false,
            justPressed: false,
            timestamp: Date.now()
        });
    }
    
    onMouseMove(event) {
        const rect = this.game.canvas.getBoundingClientRect();
        this.mouse.x = event.clientX - rect.left;
        this.mouse.y = event.clientY - rect.top;
    }
    
    onMouseWheel(event) {
        this.mouse.wheel = event.deltaY;
        event.preventDefault();
    }
    
    onTouchStart(event) {
        event.preventDefault();
        const touch = event.touches[0];
        const rect = this.game.canvas.getBoundingClientRect();
        
        this.mouse.x = touch.clientX - rect.left;
        this.mouse.y = touch.clientY - rect.top;
        
        // Simulate left mouse button
        this.mouse.buttons.set(0, {
            pressed: true,
            justPressed: true,
            timestamp: Date.now()
        });
        
        this.handleMouseClick(0, this.mouse.x, this.mouse.y);
    }
    
    onTouchEnd(event) {
        event.preventDefault();
        
        this.mouse.buttons.set(0, {
            pressed: false,
            justPressed: false,
            timestamp: Date.now()
        });
    }
    
    onTouchMove(event) {
        event.preventDefault();
        const touch = event.touches[0];
        const rect = this.game.canvas.getBoundingClientRect();
        
        this.mouse.x = touch.clientX - rect.left;
        this.mouse.y = touch.clientY - rect.top;
    }
    
    // Action handling
    handleAction(action) {
        const handler = this.actionHandlers.get(action);
        if (handler) {
            handler();
        }
    }
    
    handleMovement(dx, dy) {
        const player = this.game.characterEngine.getPlayer();
        if (player && this.game.gameState === 'playing') {
            player.move(dx, dy);
        }
    }
    
    handleInteract() {
        const player = this.game.characterEngine.getPlayer();
        if (player) {
            this.game.worldEngine.interact(player.x, player.y);
        }
    }
    
    handleMouseClick(button, x, y) {
        // Convert screen coordinates to world coordinates
        const worldPos = this.screenToWorld(x, y);
        
        if (button === 0) { // Left click
            this.game.worldEngine.handleClick(worldPos.x, worldPos.y);
        } else if (button === 2) { // Right click
            this.game.worldEngine.handleRightClick(worldPos.x, worldPos.y);
        }
    }
    
    toggleMenu() {
        const menu = document.getElementById('menu');
        menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
    }
    
    useQuickSlot(slotIndex) {
        const player = this.game.characterEngine.getPlayer();
        if (player) {
            player.useQuickSlot(slotIndex);
        }
    }
    
    toggleDebug() {
        this.game.settings.showDebug = !this.game.settings.showDebug;
        console.log('Debug mode:', this.game.settings.showDebug ? 'ON' : 'OFF');
    }
    
    // Utility methods
    isKeyPressed(keyCode) {
        const key = this.keys.get(keyCode);
        return key ? key.pressed : false;
    }
    
    isKeyJustPressed(keyCode) {
        const key = this.keys.get(keyCode);
        return key ? key.justPressed : false;
    }
    
    isMouseButtonPressed(button) {
        const mouseButton = this.mouse.buttons.get(button);
        return mouseButton ? mouseButton.pressed : false;
    }
    
    getMousePosition() {
        return { x: this.mouse.x, y: this.mouse.y };
    }
    
    screenToWorld(screenX, screenY) {
        // Convert screen coordinates to world coordinates
        // This will be implemented based on camera/viewport system
        return {
            x: Math.floor(screenX / this.game.settings.pixelScale),
            y: Math.floor(screenY / this.game.settings.pixelScale)
        };
    }
    
    update(deltaTime) {
        // Reset just-pressed states
        for (const [keyCode, keyState] of this.keys) {
            if (keyState.justPressed) {
                keyState.justPressed = false;
            }
        }
        
        for (const [button, buttonState] of this.mouse.buttons) {
            if (buttonState.justPressed) {
                buttonState.justPressed = false;
            }
        }
        
        // Reset mouse wheel
        this.mouse.wheel = 0;
    }
}
