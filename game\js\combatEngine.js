/**
 * Combat Engine - Manages turn-based combat system
 */

class CombatEngine {
    constructor(game) {
        this.game = game;
        
        // Combat state
        this.inCombat = false;
        this.combatants = [];
        this.currentTurn = 0;
        this.turnOrder = [];
        this.selectedAction = null;
        this.selectedTarget = null;
        
        // Combat UI
        this.combatUI = {
            visible: false,
            selectedOption: 0,
            actionMenu: ['Attack', 'Defend', 'Use Item', 'Run']
        };
        
        // Enemy definitions
        this.enemyDefinitions = {
            wild_boar: {
                name: 'Wild Boar',
                sprite: 'wild_boar',
                stats: {
                    hp: 40, maxHp: 40,
                    str: 25, def: 15,
                    spd: 20, int: 5
                },
                skills: ['charge', 'bite'],
                loot: [
                    { id: 'meat', quantity: 2, chance: 0.8 },
                    { id: 'hide', quantity: 1, chance: 0.6 }
                ],
                experience: 15
            },
            stone_statue: {
                name: 'Animated Stone Statue',
                sprite: 'stone_statue',
                stats: {
                    hp: 60, maxHp: 60,
                    str: 30, def: 25,
                    spd: 10, int: 0
                },
                skills: ['stone_throw', 'heavy_slam'],
                loot: [
                    { id: 'stone', quantity: 5, chance: 1.0 },
                    { id: 'rare_mineral', quantity: 1, chance: 0.3 }
                ],
                experience: 25
            },
            empire_soldier: {
                name: 'Empire Soldier',
                sprite: 'empire_soldier',
                stats: {
                    hp: 80, maxHp: 80,
                    str: 35, def: 20,
                    spd: 25, int: 15
                },
                skills: ['spear_thrust', 'shield_bash', 'battle_cry'],
                loot: [
                    { id: 'spear', quantity: 1, chance: 0.5 },
                    { id: 'shield', quantity: 1, chance: 0.3 }
                ],
                experience: 40
            }
        };
        
        // Skill definitions
        this.skillDefinitions = {
            // Player skills
            scientific_analysis: {
                name: 'Scientific Analysis',
                description: 'Analyze enemy weaknesses',
                type: 'support',
                cost: 10,
                effect: 'reveal_stats'
            },
            formula_bomb: {
                name: 'Formula Bomb',
                description: 'Create an explosive chemical reaction',
                type: 'attack',
                cost: 20,
                damage: 50,
                element: 'fire'
            },
            
            // Enemy skills
            charge: {
                name: 'Charge',
                description: 'Rush at the target with great force',
                type: 'attack',
                damage: 20,
                accuracy: 0.8
            },
            bite: {
                name: 'Bite',
                description: 'Savage bite attack',
                type: 'attack',
                damage: 15,
                accuracy: 0.9
            },
            stone_throw: {
                name: 'Stone Throw',
                description: 'Hurl a large stone',
                type: 'attack',
                damage: 25,
                accuracy: 0.7
            }
        };
    }
    
    async init() {
        console.log('Initializing Combat Engine...');
        
        // Set up combat event listeners
        this.setupEventListeners();
        
        console.log('Combat Engine initialized');
    }
    
    setupEventListeners() {
        // Listen for combat triggers
        this.game.gameManager.on('enemyEncountered', (enemy) => {
            this.startCombat([enemy]);
        });
    }
    
    startCombat(enemies) {
        console.log('Combat started!');
        
        this.inCombat = true;
        this.combatants = [];
        
        // Add player party
        const player = this.game.characterEngine.getPlayer();
        if (player) {
            this.combatants.push(new Combatant(player, 'player'));
        }
        
        const party = this.game.characterEngine.getParty();
        for (const member of party) {
            this.combatants.push(new Combatant(member, 'ally'));
        }
        
        // Add enemies
        for (const enemy of enemies) {
            this.combatants.push(new Combatant(enemy, 'enemy'));
        }
        
        // Determine turn order
        this.calculateTurnOrder();
        
        // Start first turn
        this.currentTurn = 0;
        this.startTurn();
        
        // Show combat UI
        this.combatUI.visible = true;
    }
    
    calculateTurnOrder() {
        // Sort by speed (highest first)
        this.turnOrder = [...this.combatants].sort((a, b) => {
            return b.character.stats.spd - a.character.stats.spd;
        });
    }
    
    startTurn() {
        const currentCombatant = this.getCurrentCombatant();
        if (!currentCombatant) {
            this.endCombat();
            return;
        }
        
        console.log(`${currentCombatant.character.name}'s turn`);
        
        if (currentCombatant.side === 'player') {
            // Player turn - wait for input
            this.combatUI.selectedOption = 0;
        } else {
            // AI turn
            this.performAIAction(currentCombatant);
        }
    }
    
    getCurrentCombatant() {
        if (this.currentTurn >= this.turnOrder.length) {
            return null;
        }
        
        const combatant = this.turnOrder[this.currentTurn];
        
        // Skip if combatant is defeated
        if (combatant.isDefeated()) {
            this.nextTurn();
            return this.getCurrentCombatant();
        }
        
        return combatant;
    }
    
    performPlayerAction(action, target = null) {
        const currentCombatant = this.getCurrentCombatant();
        if (!currentCombatant || currentCombatant.side !== 'player') {
            return;
        }
        
        switch (action) {
            case 'Attack':
                this.performAttack(currentCombatant, target || this.getRandomEnemy());
                break;
                
            case 'Defend':
                this.performDefend(currentCombatant);
                break;
                
            case 'Use Item':
                // TODO: Implement item usage
                console.log('Item usage not implemented yet');
                break;
                
            case 'Run':
                this.attemptRun();
                break;
        }
        
        this.nextTurn();
    }
    
    performAIAction(combatant) {
        // Simple AI: attack a random player character
        const targets = this.combatants.filter(c => 
            (c.side === 'player' || c.side === 'ally') && !c.isDefeated()
        );
        
        if (targets.length > 0) {
            const target = targets[Math.floor(Math.random() * targets.length)];
            this.performAttack(combatant, target);
        }
        
        // Auto-advance turn after AI action
        setTimeout(() => {
            this.nextTurn();
        }, 1000);
    }
    
    performAttack(attacker, target) {
        if (!target || target.isDefeated()) {
            console.log('Invalid target');
            return;
        }
        
        const damage = this.calculateDamage(attacker, target);
        target.takeDamage(damage);
        
        console.log(`${attacker.character.name} attacks ${target.character.name} for ${damage} damage!`);
        
        if (target.isDefeated()) {
            console.log(`${target.character.name} is defeated!`);
            this.onCombatantDefeated(target);
        }
    }
    
    performDefend(combatant) {
        combatant.defending = true;
        console.log(`${combatant.character.name} defends!`);
    }
    
    calculateDamage(attacker, target) {
        const baseDamage = attacker.character.stats.str;
        const defense = target.character.stats.def;
        const defendBonus = target.defending ? defense * 0.5 : 0;
        
        const damage = Math.max(1, baseDamage - defense - defendBonus);
        
        // Add some randomness
        const variance = 0.2;
        const randomFactor = 1 + (Math.random() - 0.5) * variance;
        
        return Math.round(damage * randomFactor);
    }
    
    attemptRun() {
        const runChance = 0.7; // 70% chance to run
        
        if (Math.random() < runChance) {
            console.log('Successfully ran away!');
            this.endCombat(false);
        } else {
            console.log('Could not escape!');
        }
    }
    
    nextTurn() {
        // Reset defending status
        const currentCombatant = this.getCurrentCombatant();
        if (currentCombatant) {
            currentCombatant.defending = false;
        }
        
        this.currentTurn++;
        
        // Check for combat end conditions
        if (this.checkCombatEnd()) {
            return;
        }
        
        // Start new round if all combatants have acted
        if (this.currentTurn >= this.turnOrder.length) {
            this.currentTurn = 0;
            console.log('--- New Round ---');
        }
        
        this.startTurn();
    }
    
    checkCombatEnd() {
        const playerSide = this.combatants.filter(c => 
            (c.side === 'player' || c.side === 'ally') && !c.isDefeated()
        );
        
        const enemySide = this.combatants.filter(c => 
            c.side === 'enemy' && !c.isDefeated()
        );
        
        if (playerSide.length === 0) {
            console.log('Defeat! All party members are down.');
            this.endCombat(false);
            return true;
        }
        
        if (enemySide.length === 0) {
            console.log('Victory! All enemies defeated.');
            this.endCombat(true);
            return true;
        }
        
        return false;
    }
    
    onCombatantDefeated(combatant) {
        if (combatant.side === 'enemy') {
            // Award experience and loot
            this.awardExperience(combatant);
            this.awardLoot(combatant);
        }
    }
    
    awardExperience(defeatedEnemy) {
        const experience = defeatedEnemy.character.experience || 15;
        
        const playerParty = this.combatants.filter(c => 
            (c.side === 'player' || c.side === 'ally') && !c.isDefeated()
        );
        
        for (const combatant of playerParty) {
            combatant.character.gainExperience(experience);
        }
        
        console.log(`Party gained ${experience} experience!`);
    }
    
    awardLoot(defeatedEnemy) {
        const player = this.game.characterEngine.getPlayer();
        if (!player || !player.inventory) return;
        
        const loot = defeatedEnemy.character.loot || [];
        
        for (const item of loot) {
            if (Math.random() < item.chance) {
                player.inventory.addItem(item.id, item.quantity);
                console.log(`Found: ${item.quantity}x ${item.id}`);
            }
        }
    }
    
    endCombat(victory = true) {
        console.log('Combat ended');
        
        this.inCombat = false;
        this.combatants = [];
        this.turnOrder = [];
        this.currentTurn = 0;
        this.combatUI.visible = false;
        
        if (victory) {
            // Award science points for victory
            this.game.gameManager.gameProgress.sciencePoints += 5;
            console.log('Gained 5 science points for victory!');
        } else {
            // Handle defeat (maybe respawn at safe location)
            console.log('Retreating to safety...');
        }
    }
    
    getRandomEnemy() {
        const enemies = this.combatants.filter(c => c.side === 'enemy' && !c.isDefeated());
        return enemies.length > 0 ? enemies[Math.floor(Math.random() * enemies.length)] : null;
    }
    
    // Input handling
    handleCombatInput(action) {
        if (!this.inCombat || !this.combatUI.visible) return;
        
        const currentCombatant = this.getCurrentCombatant();
        if (!currentCombatant || currentCombatant.side !== 'player') return;
        
        if (typeof action === 'string') {
            this.performPlayerAction(action);
        }
    }
    
    update(deltaTime) {
        // Update combat animations and effects
        if (this.inCombat) {
            for (const combatant of this.combatants) {
                combatant.update(deltaTime);
            }
        }
    }
    
    render(renderer) {
        if (!this.inCombat) return;
        
        // Render combat scene
        this.renderCombatScene(renderer);
        
        // Render combat UI
        if (this.combatUI.visible) {
            this.renderCombatUI(renderer);
        }
    }
    
    renderCombatScene(renderer) {
        // Simple combat layout
        const canvas = renderer.ctx.canvas;
        
        // Render player party on the left
        const playerParty = this.combatants.filter(c => c.side === 'player' || c.side === 'ally');
        for (let i = 0; i < playerParty.length; i++) {
            const combatant = playerParty[i];
            const x = 100;
            const y = 200 + i * 80;
            
            combatant.render(renderer, x, y);
        }
        
        // Render enemies on the right
        const enemies = this.combatants.filter(c => c.side === 'enemy');
        for (let i = 0; i < enemies.length; i++) {
            const combatant = enemies[i];
            const x = canvas.width - 200;
            const y = 200 + i * 80;
            
            combatant.render(renderer, x, y);
        }
    }
    
    renderCombatUI(renderer) {
        const canvas = renderer.ctx.canvas;
        const currentCombatant = this.getCurrentCombatant();
        
        if (!currentCombatant || currentCombatant.side !== 'player') {
            // Show "Enemy Turn" or waiting message
            renderer.drawUIText('Enemy Turn...', canvas.width / 2 - 50, canvas.height - 100, {
                font: '16px monospace',
                color: '#FF5722'
            });
            return;
        }
        
        // Action menu
        const menuX = 50;
        const menuY = canvas.height - 150;
        const menuWidth = 200;
        const menuHeight = 120;
        
        renderer.drawUIRect(menuX, menuY, menuWidth, menuHeight, 'rgba(0, 0, 0, 0.8)', true);
        renderer.drawUIRect(menuX, menuY, menuWidth, menuHeight, '#666', false);
        
        // Menu options
        for (let i = 0; i < this.combatUI.actionMenu.length; i++) {
            const option = this.combatUI.actionMenu[i];
            const optionY = menuY + 20 + i * 25;
            const color = i === this.combatUI.selectedOption ? '#FFEB3B' : 'white';
            
            renderer.drawUIText(`${i === this.combatUI.selectedOption ? '>' : ' '} ${option}`, 
                menuX + 10, optionY, {
                font: '12px monospace',
                color: color
            });
        }
        
        // Current character info
        renderer.drawUIText(`${currentCombatant.character.name}'s Turn`, 
            canvas.width / 2 - 80, canvas.height - 180, {
            font: 'bold 14px monospace',
            color: '#4CAF50'
        });
    }
    
    // Save/Load
    getSaveData() {
        return {
            inCombat: this.inCombat,
            // Don't save active combat state - it will be reset
        };
    }
    
    loadSaveData(data) {
        // Combat state is not persistent
        this.inCombat = false;
        this.combatants = [];
        this.turnOrder = [];
        this.currentTurn = 0;
        this.combatUI.visible = false;
    }
}

/**
 * Combatant wrapper for characters in combat
 */
class Combatant {
    constructor(character, side) {
        this.character = character;
        this.side = side; // 'player', 'ally', 'enemy'
        this.defending = false;
        this.statusEffects = [];
    }
    
    isDefeated() {
        return this.character.stats.hp <= 0;
    }
    
    takeDamage(amount) {
        this.character.stats.hp = Math.max(0, this.character.stats.hp - amount);
    }
    
    heal(amount) {
        this.character.stats.hp = Math.min(
            this.character.stats.maxHp, 
            this.character.stats.hp + amount
        );
    }
    
    update(deltaTime) {
        // Update status effects, animations, etc.
    }
    
    render(renderer, x, y) {
        // Render character sprite
        const sprite = renderer.game.assets.getCharacter(this.character.sprite);
        if (sprite) {
            renderer.drawSprite(sprite, x, y);
        }
        
        // Render health bar
        const barWidth = 60;
        const barHeight = 6;
        const barY = y - 10;
        
        renderer.drawRect(x - barWidth/2, barY, barWidth, barHeight, '#333', true);
        
        const healthPercent = this.character.stats.hp / this.character.stats.maxHp;
        const healthWidth = barWidth * healthPercent;
        
        let healthColor = '#4CAF50';
        if (healthPercent < 0.3) healthColor = '#F44336';
        else if (healthPercent < 0.6) healthColor = '#FF9800';
        
        renderer.drawRect(x - barWidth/2, barY, healthWidth, barHeight, healthColor, true);
        
        // Character name
        renderer.drawText(this.character.name, x, y - 25, {
            font: '10px monospace',
            color: 'white',
            align: 'center'
        });
        
        // Status indicators
        if (this.defending) {
            renderer.drawText('DEF', x + 20, y - 15, {
                font: '8px monospace',
                color: '#2196F3'
            });
        }
        
        if (this.isDefeated()) {
            renderer.drawText('KO', x, y, {
                font: 'bold 12px monospace',
                color: '#F44336',
                align: 'center'
            });
        }
    }
}
