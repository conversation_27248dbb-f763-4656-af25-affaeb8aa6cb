/**
 * World Engine - Manages the game world, environments, and interactions
 */

class WorldEngine {
    constructor(game) {
        this.game = game;
        
        // World data
        this.currentArea = 'starting_forest';
        this.areas = new Map();
        this.worldObjects = new Map();
        
        // World settings
        this.worldSize = { width: 2000, height: 2000 };
        this.tileSize = 16;
        
        // Area definitions
        this.areaDefinitions = {
            starting_forest: {
                name: 'Starting Forest',
                description: 'A lush forest where <PERSON><PERSON> first awakened.',
                size: { width: 100, height: 100 },
                tilemap: null, // Will be generated
                objects: [
                    { type: 'tree', x: 20, y: 15 },
                    { type: 'stone_deposit', x: 35, y: 25 },
                    { type: 'wood_pile', x: 45, y: 30 },
                    { type: 'cave_entrance', x: 80, y: 20 }
                ],
                npcs: [],
                resources: ['stone', 'wood', 'berries']
            },
            cave_of_miracles: {
                name: 'Cave of Miracles',
                description: 'The mysterious cave containing the revival fluid.',
                size: { width: 50, height: 50 },
                tilemap: null,
                objects: [
                    { type: 'revival_spring', x: 25, y: 25 },
                    { type: 'crystal_formation', x: 15, y: 20 },
                    { type: 'ancient_statue', x: 35, y: 30 }
                ],
                npcs: [],
                resources: ['nitric_acid', 'crystals', 'rare_minerals']
            },
            ishigami_village: {
                name: 'Ishigami Village',
                description: 'A primitive village of stone age survivors.',
                size: { width: 80, height: 80 },
                tilemap: null,
                objects: [
                    { type: 'village_hut', x: 20, y: 20 },
                    { type: 'village_hut', x: 30, y: 25 },
                    { type: 'fire_pit', x: 40, y: 40 },
                    { type: 'storage_shed', x: 50, y: 30 }
                ],
                npcs: ['kohaku', 'chrome'],
                resources: ['food', 'primitive_tools', 'animal_hide']
            }
        };
        
        // Object definitions
        this.objectDefinitions = {
            tree: {
                name: 'Tree',
                sprite: 'tree',
                solid: true,
                harvestable: true,
                resources: [{ id: 'wood', quantity: 2, chance: 1.0 }],
                tool_required: 'stone_axe'
            },
            stone_deposit: {
                name: 'Stone Deposit',
                sprite: 'stone',
                solid: true,
                harvestable: true,
                resources: [{ id: 'stone', quantity: 3, chance: 1.0 }],
                tool_required: null
            },
            wood_pile: {
                name: 'Wood Pile',
                sprite: 'wood_item',
                solid: false,
                harvestable: true,
                resources: [{ id: 'wood', quantity: 1, chance: 1.0 }],
                tool_required: null
            },
            cave_entrance: {
                name: 'Cave Entrance',
                sprite: 'cave_wall',
                solid: false,
                interactive: true,
                action: 'enter_cave'
            },
            revival_spring: {
                name: 'Revival Spring',
                sprite: 'water',
                solid: false,
                interactive: true,
                action: 'collect_revival_fluid'
            }
        };
    }
    
    async init() {
        console.log('Initializing World Engine...');
        
        // Generate initial areas
        this.generateAreas();
        
        // Load starting area
        this.loadArea(this.currentArea);
        
        console.log('World Engine initialized');
    }
    
    generateAreas() {
        for (const [areaId, definition] of Object.entries(this.areaDefinitions)) {
            const area = new GameArea(areaId, definition);
            area.generateTilemap();
            this.areas.set(areaId, area);
        }
    }
    
    loadArea(areaId) {
        const area = this.areas.get(areaId);
        if (!area) {
            console.error(`Area not found: ${areaId}`);
            return false;
        }
        
        this.currentArea = areaId;
        
        // Clear existing world objects
        this.worldObjects.clear();
        
        // Load area objects
        for (const objDef of area.definition.objects) {
            const obj = new WorldObject(objDef.type, objDef.x, objDef.y, this.objectDefinitions[objDef.type]);
            this.worldObjects.set(`${objDef.x}_${objDef.y}`, obj);
        }
        
        // Spawn NPCs
        for (const npcId of area.definition.npcs) {
            if (!this.game.characterEngine.getNPC(npcId)) {
                // Find a suitable spawn position
                const spawnPos = this.findSpawnPosition(area);
                this.game.characterEngine.createNPC(npcId, spawnPos.x, spawnPos.y);
            }
        }
        
        console.log(`Loaded area: ${area.name}`);
        return true;
    }
    
    findSpawnPosition(area) {
        // Simple spawn position finding - avoid objects
        let attempts = 0;
        while (attempts < 100) {
            const x = Math.random() * area.definition.size.width * this.tileSize;
            const y = Math.random() * area.definition.size.height * this.tileSize;
            
            if (!this.isPositionBlocked(x, y)) {
                return { x, y };
            }
            attempts++;
        }
        
        // Fallback to center
        return {
            x: area.definition.size.width * this.tileSize / 2,
            y: area.definition.size.height * this.tileSize / 2
        };
    }
    
    isPositionBlocked(x, y) {
        const tileX = Math.floor(x / this.tileSize);
        const tileY = Math.floor(y / this.tileSize);
        const key = `${tileX}_${tileY}`;
        
        const obj = this.worldObjects.get(key);
        return obj && obj.solid;
    }
    
    update(deltaTime) {
        // Update world objects
        for (const obj of this.worldObjects.values()) {
            obj.update(deltaTime);
        }
        
        // Update current area
        const currentArea = this.areas.get(this.currentArea);
        if (currentArea) {
            currentArea.update(deltaTime);
        }
    }
    
    render(renderer) {
        const currentArea = this.areas.get(this.currentArea);
        if (!currentArea) return;
        
        // Render tilemap
        currentArea.render(renderer);
        
        // Render world objects
        for (const obj of this.worldObjects.values()) {
            obj.render(renderer);
        }
    }
    
    // Interaction methods
    interact(x, y) {
        const tileX = Math.floor(x / this.tileSize);
        const tileY = Math.floor(y / this.tileSize);
        
        // Check for interactive objects in adjacent tiles
        for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
                const checkX = tileX + dx;
                const checkY = tileY + dy;
                const key = `${checkX}_${checkY}`;
                
                const obj = this.worldObjects.get(key);
                if (obj && obj.interactive) {
                    this.handleObjectInteraction(obj);
                    return true;
                }
                
                if (obj && obj.harvestable) {
                    this.handleObjectHarvest(obj);
                    return true;
                }
            }
        }
        
        return false;
    }
    
    handleClick(x, y) {
        // Handle mouse clicks on the world
        this.interact(x, y);
    }
    
    handleRightClick(x, y) {
        // Handle right-click actions (maybe examine objects)
        const tileX = Math.floor(x / this.tileSize);
        const tileY = Math.floor(y / this.tileSize);
        const key = `${tileX}_${tileY}`;
        
        const obj = this.worldObjects.get(key);
        if (obj) {
            console.log(`Examining: ${obj.name}`);
            // TODO: Show object description
        }
    }
    
    handleObjectInteraction(obj) {
        console.log(`Interacting with: ${obj.name}`);
        
        switch (obj.action) {
            case 'enter_cave':
                this.loadArea('cave_of_miracles');
                break;
                
            case 'collect_revival_fluid':
                this.collectRevivalFluid();
                break;
                
            default:
                console.log(`Unknown interaction: ${obj.action}`);
        }
    }
    
    handleObjectHarvest(obj) {
        const player = this.game.characterEngine.getPlayer();
        if (!player) return;
        
        // Check if player has required tool
        if (obj.tool_required && !player.inventory.hasItem(obj.tool_required)) {
            console.log(`Need ${obj.tool_required} to harvest ${obj.name}`);
            return;
        }
        
        // Harvest resources
        for (const resource of obj.resources) {
            if (Math.random() < resource.chance) {
                player.inventory.addItem(resource.id, resource.quantity);
                console.log(`Harvested ${resource.quantity} ${resource.id}`);
            }
        }
        
        // Remove object or mark as harvested
        obj.harvest();
    }
    
    collectRevivalFluid() {
        const player = this.game.characterEngine.getPlayer();
        if (player) {
            player.inventory.addItem('revival_fluid', 1);
            console.log('Collected Revival Fluid!');
            
            // Trigger story event
            this.game.gameManager.emit('revivalFluidCollected');
        }
    }
    
    // Area transition
    changeArea(newAreaId, spawnX = null, spawnY = null) {
        if (this.loadArea(newAreaId)) {
            const player = this.game.characterEngine.getPlayer();
            if (player && spawnX !== null && spawnY !== null) {
                player.x = spawnX;
                player.y = spawnY;
            }
            return true;
        }
        return false;
    }
    
    getCurrentArea() {
        return this.areas.get(this.currentArea);
    }
    
    // Save/Load
    getSaveData() {
        return {
            currentArea: this.currentArea,
            worldObjects: Array.from(this.worldObjects.entries()).map(([key, obj]) => ({
                key,
                data: obj.getSaveData()
            }))
        };
    }
    
    loadSaveData(data) {
        this.currentArea = data.currentArea;
        
        this.worldObjects.clear();
        for (const objEntry of data.worldObjects) {
            const obj = WorldObject.fromSaveData(objEntry.data);
            this.worldObjects.set(objEntry.key, obj);
        }
        
        this.loadArea(this.currentArea);
    }
}

/**
 * Game Area class
 */
class GameArea {
    constructor(id, definition) {
        this.id = id;
        this.definition = definition;
        this.name = definition.name;
        this.tilemap = null;
    }
    
    generateTilemap() {
        const { width, height } = this.definition.size;
        this.tilemap = [];
        
        for (let y = 0; y < height; y++) {
            const row = [];
            for (let x = 0; x < width; x++) {
                // Simple terrain generation
                let tile = 'grass';
                
                if (this.id === 'cave_of_miracles') {
                    tile = 'cave_wall';
                    if (x > 5 && x < width - 5 && y > 5 && y < height - 5) {
                        tile = 'stone';
                    }
                } else if (this.id === 'ishigami_village') {
                    tile = 'village_ground';
                }
                
                row.push(tile);
            }
            this.tilemap.push(row);
        }
    }
    
    update(deltaTime) {
        // Update area-specific logic
    }
    
    render(renderer) {
        if (!this.tilemap) return;
        
        const tileSize = 16;
        
        for (let y = 0; y < this.tilemap.length; y++) {
            for (let x = 0; x < this.tilemap[y].length; x++) {
                const tileType = this.tilemap[y][x];
                const tile = renderer.game.assets.getTile(tileType);
                
                if (tile) {
                    renderer.drawTile(tile, x, y);
                }
            }
        }
    }
}

/**
 * World Object class
 */
class WorldObject {
    constructor(type, x, y, definition) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.definition = definition;
        this.name = definition.name;
        this.sprite = definition.sprite;
        this.solid = definition.solid || false;
        this.interactive = definition.interactive || false;
        this.harvestable = definition.harvestable || false;
        this.action = definition.action;
        this.resources = definition.resources || [];
        this.tool_required = definition.tool_required;
        
        // State
        this.harvested = false;
        this.visible = true;
    }
    
    update(deltaTime) {
        // Update object logic
    }
    
    render(renderer) {
        if (!this.visible || this.harvested) return;
        
        const sprite = renderer.game.assets.get(this.sprite);
        if (sprite) {
            renderer.drawSprite(sprite, this.x * 16, this.y * 16);
        }
    }
    
    harvest() {
        this.harvested = true;
        console.log(`${this.name} harvested`);
    }
    
    getSaveData() {
        return {
            type: this.type,
            x: this.x,
            y: this.y,
            harvested: this.harvested,
            visible: this.visible
        };
    }
    
    static fromSaveData(data) {
        // This would need access to object definitions
        // For now, return a basic object
        const obj = new WorldObject(data.type, data.x, data.y, {});
        obj.harvested = data.harvested;
        obj.visible = data.visible;
        return obj;
    }
}
